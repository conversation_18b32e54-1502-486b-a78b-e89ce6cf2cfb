"""
运行教师模型（规则算法）
"""
import os
import time
import argparse
import math
import numpy as np
from typing import List, Tuple

from environment.drone_env import DroneEnvironment
from models.teacher_model import TeacherModel
from utils.visualization import visualize_drone_state, plot_trajectory, plot_depth_image, plot_distance_data, plot_point_cloud
from config import MIN_OBSTACLE_DISTANCE, FORWARD_SPEED

def parse_args():
    """
    解析命令行参数
    """
    parser = argparse.ArgumentParser(description="Run teacher model for obstacle avoidance")
    parser.add_argument("--max_steps", type=int, default=1000, help="Maximum steps to run")
    parser.add_argument("--visualize", action="store_true", help="Visualize drone state")
    parser.add_argument("--save_path", type=str, default="results/teacher", help="Path to save results")
    parser.add_argument("--disable_collision", action="store_true", help="Disable collision detection")
    parser.add_argument("--min_distance", type=float, default=MIN_OBSTACLE_DISTANCE, help="Minimum obstacle distance (meters)")

    return parser.parse_args()

def main():
    """
    主函数
    """
    # 解析命令行参数
    args = parse_args()

    # 创建保存目录
    os.makedirs(args.save_path, exist_ok=True)

    # 创建环境和模型
    env = DroneEnvironment(
        enable_collision_detection=not args.disable_collision,
        min_obstacle_distance=args.min_distance
    )
    teacher = TeacherModel(min_distance=args.min_distance)

    # 打印碰撞检测状态
    if args.disable_collision:
        print("碰撞检测已禁用")
    else:
        print("碰撞检测已启用")

    # 打印避障参数
    print(f"最小障碍物距离: {args.min_distance}米")
    print(f"警告距离阈值: {args.min_distance * 1.5}米")

    # 重置环境
    observation = env.reset()

    # 记录轨迹
    positions = []

    try:
        # 运行模型
        for step in range(args.max_steps):
            # 获取当前位置
            drone_state = env.client.getMultirotorState(vehicle_name=env.drone_name)
            position = drone_state.kinematics_estimated.position
            positions.append((position.x_val, position.y_val))

            # 获取当前高度
            current_height = env.get_current_height()

            # 使用教师模型预测3D速度向量
            velocity_vector = teacher.get_velocity_vector(observation, current_height)

            # 为了兼容可视化，计算等效的转向角
            vx, vy, vz = velocity_vector
            if abs(vx) > 0.01:
                action = np.degrees(np.arctan2(vy, vx))  # 等效转向角，用于可视化
            else:
                action = 0.0

            # 可视化
            if args.visualize and step % 10 == 0:
                # 创建步骤目录
                step_dir = os.path.join(args.save_path, f"step_{step:04d}")
                os.makedirs(step_dir, exist_ok=True)

                # 可视化无人机状态
                visualize_path = os.path.join(step_dir, "drone_state.png")
                visualize_drone_state(
                    (position.x_val, position.y_val, position.z_val),
                    env.current_yaw,
                    observation,
                    save_path=visualize_path,
                    distance_array=env.last_distance_array
                )

                # 可视化深度图像（如果是深度图像）
                if len(observation.shape) == 2:  # 深度图像
                    depth_img_path = os.path.join(step_dir, "depth_image.png")
                    plot_depth_image(observation, action, depth_img_path)

                    # 可视化点云（如果有）
                    if env.last_point_cloud is not None and len(env.last_point_cloud) > 0:
                        point_cloud_path = os.path.join(step_dir, "point_cloud.png")
                        plot_point_cloud(env.last_point_cloud, point_cloud_path)

                # 可视化距离数组（如果有）
                if env.last_distance_array is not None:
                    distance_array_path = os.path.join(step_dir, "distance_array.png")
                    plot_distance_data(env.last_distance_array, action, distance_array_path)

            # 执行动作 - 使用3D速度向量
            next_observation, reward, done, info = env.step(velocity_vector)

            # 获取速度信息（世界坐标系）
            velocity = drone_state.kinematics_estimated.linear_velocity
            speed = math.sqrt(velocity.x_val**2 + velocity.y_val**2 + velocity.z_val**2)

            # 获取无人机姿态（四元数）
            orientation = drone_state.kinematics_estimated.orientation

            # 将世界坐标系速度转换为机体坐标系速度
            # 创建四元数
            q = np.array([orientation.w_val, orientation.x_val, orientation.y_val, orientation.z_val])

            # 创建旋转矩阵
            def quaternion_to_rotation_matrix(q):
                """四元数转旋转矩阵"""
                w, x, y, z = q
                return np.array([
                    [1 - 2*y*y - 2*z*z, 2*x*y - 2*w*z, 2*x*z + 2*w*y],
                    [2*x*y + 2*w*z, 1 - 2*x*x - 2*z*z, 2*y*z - 2*w*x],
                    [2*x*z - 2*w*y, 2*y*z + 2*w*x, 1 - 2*x*x - 2*y*y]
                ])

            # 世界坐标系到机体坐标系的旋转矩阵是旋转矩阵的转置
            R = quaternion_to_rotation_matrix(q).T

            # 世界坐标系中的速度向量
            v_world = np.array([velocity.x_val, velocity.y_val, velocity.z_val])

            # 转换为机体坐标系中的速度
            v_body = R.dot(v_world)

            # 打印信息
            print(f"Step {step + 1}/{args.max_steps}, Reward: {reward:.2f}")
            print(f"Command Velocity: Vx={vx:.2f}, Vy={vy:.2f}, Vz={vz:.2f} m/s")
            print(f"Equivalent Turn Angle: {action:.2f}°")
            print(f"Current Height: {current_height:.2f}m")
            print(f"Actual Speed: {speed:.2f} m/s, World Velocity: [{velocity.x_val:.2f}, {velocity.y_val:.2f}, {velocity.z_val:.2f}]")
            print(f"Body Velocity: [{v_body[0]:.2f}, {v_body[1]:.2f}, {v_body[2]:.2f}]")

            # 更新观测
            observation = next_observation

            # 如果回合结束，退出循环
            if done:
                print(f"Episode ended after {step + 1} steps due to collision")
                break

            # 等待一小段时间，以便观察
            time.sleep(0.05)

    except KeyboardInterrupt:
        print("Interrupted by user")

    finally:
        # 绘制轨迹
        trajectory_path = os.path.join(args.save_path, "trajectory.png")
        plot_trajectory(positions, save_path=trajectory_path)

        # 关闭环境
        env.close()

        print("Run completed")

if __name__ == "__main__":
    main()
