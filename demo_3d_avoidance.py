#!/usr/bin/env python3
"""
3D避障系统演示
"""
import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models.teacher_model import TeacherModel
from environment.depth_utils import create_3d_grid_from_points, generate_3d_velocity_candidates, evaluate_3d_velocity_safety
from config import *

def create_complex_obstacle_scene():
    """创建复杂的障碍物场景"""
    obstacles = []
    
    # 前方障碍物墙
    for x in np.linspace(8, 10, 20):
        for y in np.linspace(-2, 2, 20):
            for z in np.linspace(-1, 1, 10):
                obstacles.append([x, y, z])
    
    # 左侧障碍物
    for x in np.linspace(5, 12, 15):
        for y in np.linspace(-4, -3, 10):
            for z in np.linspace(-0.5, 0.5, 5):
                obstacles.append([x, y, z])
    
    # 上方障碍物（天花板）
    for x in np.linspace(6, 14, 15):
        for y in np.linspace(-1, 1, 10):
            for z in np.linspace(-2.5, -2, 5):
                obstacles.append([x, y, z])
    
    return np.array(obstacles)

def visualize_3d_scene(obstacles, velocity_vector, title="3D避障场景"):
    """可视化3D场景"""
    fig = plt.figure(figsize=(12, 8))
    ax = fig.add_subplot(111, projection='3d')
    
    # 绘制障碍物
    if len(obstacles) > 0:
        ax.scatter(obstacles[:, 0], obstacles[:, 1], obstacles[:, 2], 
                  c='red', alpha=0.6, s=20, label='障碍物')
    
    # 绘制无人机位置（原点）
    ax.scatter([0], [0], [0], c='blue', s=100, marker='^', label='无人机')
    
    # 绘制速度向量
    vx, vy, vz = velocity_vector
    ax.quiver(0, 0, 0, vx*3, vy*3, vz*3, 
             color='green', arrow_length_ratio=0.1, linewidth=3, label='速度向量')
    
    # 设置坐标轴
    ax.set_xlabel('X (前方)')
    ax.set_ylabel('Y (右方)')
    ax.set_zlabel('Z (下方)')
    ax.set_title(title)
    ax.legend()
    
    # 设置相同的比例
    max_range = 15
    ax.set_xlim([0, max_range])
    ax.set_ylim([-max_range/2, max_range/2])
    ax.set_zlim([-max_range/2, max_range/2])
    
    plt.tight_layout()
    return fig

def demo_scenario(scenario_name, obstacles, height, teacher):
    """演示单个场景"""
    print(f"\n{'='*60}")
    print(f"场景: {scenario_name}")
    print(f"障碍物数量: {len(obstacles)}")
    print(f"当前高度: {height:.1f}m")
    print('='*60)
    
    # 创建模拟深度图像（这里简化处理）
    depth_image = np.random.rand(DEPTH_HEIGHT, DEPTH_WIDTH) * 10 + 5
    
    # 获取3D速度向量
    velocity_vector = teacher.get_velocity_vector(depth_image, height)
    
    vx, vy, vz = velocity_vector
    total_speed = np.linalg.norm(velocity_vector)
    horizontal_speed = np.linalg.norm(velocity_vector[:2])
    
    print(f"\n结果分析:")
    print(f"  输出速度向量: Vx={vx:.2f}, Vy={vy:.2f}, Vz={vz:.2f} m/s")
    print(f"  总速度: {total_speed:.2f} m/s")
    print(f"  水平速度: {horizontal_speed:.2f} m/s")
    
    # 分析避障策略
    strategy = []
    if abs(vx) < 0.3:
        strategy.append("减速")
    elif vx > 0.8:
        strategy.append("正常前进")
    
    if abs(vy) > 0.3:
        direction = "右转" if vy > 0 else "左转"
        strategy.append(direction)
    
    if abs(vz) > 0.2:
        direction = "上升" if vz < 0 else "下降"  # 注意z轴方向
        strategy.append(direction)
    
    if not strategy:
        strategy.append("直行")
    
    print(f"  避障策略: {', '.join(strategy)}")
    
    # 可视化
    fig = visualize_3d_scene(obstacles, velocity_vector, f"{scenario_name} - 3D避障")
    
    return velocity_vector, fig

def main():
    """主演示函数"""
    print("🚁 3D避障系统演示")
    print("="*60)
    
    # 创建教师模型
    teacher = TeacherModel()
    
    # 定义演示场景
    scenarios = [
        {
            "name": "场景1: 前方障碍物墙",
            "obstacles": create_complex_obstacle_scene(),
            "height": 3.0,
            "description": "前方有障碍物墙，需要绕行或改变高度"
        },
        {
            "name": "场景2: 左侧通道",
            "obstacles": np.array([
                # 右侧障碍物
                [6, 2, 0], [7, 2, 0], [8, 2, 0], [9, 2, 0],
                [6, 3, 0], [7, 3, 0], [8, 3, 0], [9, 3, 0],
                # 上方障碍物
                [6, 0, -2], [7, 0, -2], [8, 0, -2], [9, 0, -2],
                [6, 1, -2], [7, 1, -2], [8, 1, -2], [9, 1, -2],
            ]),
            "height": 3.0,
            "description": "右侧和上方有障碍物，应该向左下方机动"
        },
        {
            "name": "场景3: 高度过高",
            "obstacles": np.array([
                [5, 0, 0], [6, 0, 0], [7, 0, 0],
                [5, 1, 0], [6, 1, 0], [7, 1, 0],
            ]),
            "height": 6.5,  # 超过警戒高度
            "description": "高度超过警戒值，触发高度限制机制"
        },
        {
            "name": "场景4: 下方障碍物",
            "obstacles": np.array([
                # 下方障碍物
                [5, 0, 2], [6, 0, 2], [7, 0, 2], [8, 0, 2],
                [5, -1, 2], [6, -1, 2], [7, -1, 2], [8, -1, 2],
                [5, 1, 2], [6, 1, 2], [7, 1, 2], [8, 1, 2],
            ]),
            "height": 2.0,
            "description": "下方有障碍物，需要上升避障"
        }
    ]
    
    # 存储结果
    results = []
    figures = []
    
    # 运行所有场景
    for scenario in scenarios:
        print(f"\n{scenario['description']}")
        
        velocity_vector, fig = demo_scenario(
            scenario["name"],
            scenario["obstacles"], 
            scenario["height"],
            teacher
        )
        
        results.append({
            "name": scenario["name"],
            "velocity": velocity_vector,
            "height": scenario["height"]
        })
        figures.append(fig)
    
    # 显示总结
    print(f"\n{'='*60}")
    print("演示总结")
    print('='*60)
    
    for i, result in enumerate(results):
        vx, vy, vz = result["velocity"]
        print(f"{i+1}. {result['name']}")
        print(f"   高度: {result['height']:.1f}m")
        print(f"   速度: Vx={vx:.2f}, Vy={vy:.2f}, Vz={vz:.2f}")
        
        # 简化的策略描述
        if abs(vy) > 0.3:
            direction = "右" if vy > 0 else "左"
            print(f"   主要策略: 向{direction}机动")
        elif abs(vz) > 0.2:
            direction = "上升" if vz < 0 else "下降"
            print(f"   主要策略: {direction}避障")
        else:
            print(f"   主要策略: 直行")
        print()
    
    print("🎉 3D避障演示完成！")
    print("\n特点总结:")
    print("✓ 真正的3D空间避障，不再局限于水平面")
    print("✓ 考虑三维距离，而非仅水平距离")
    print("✓ 支持垂直方向的机动（上升/下降）")
    print("✓ 高度限制机制确保飞行安全")
    print("✓ 基于3D网格的精确碰撞预测")
    
    # 保存图像
    try:
        import os
        os.makedirs("demo_results", exist_ok=True)
        for i, fig in enumerate(figures):
            fig.savefig(f"demo_results/3d_avoidance_scenario_{i+1}.png", dpi=150, bbox_inches='tight')
        print(f"\n📊 可视化结果已保存到 demo_results/ 目录")
    except Exception as e:
        print(f"保存图像时出错: {e}")
    
    # 显示图像（如果在支持的环境中）
    try:
        plt.show()
    except:
        print("无法显示图像，请查看保存的文件")

if __name__ == "__main__":
    main()
