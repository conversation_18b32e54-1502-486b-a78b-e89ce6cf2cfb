# 无人机避障问题分析与修复总结

## 问题描述

从运行日志中发现，无人机在遇到障碍物时无法有效避障，表现为：

1. **无人机一直直行**：速度向量始终相同，没有根据障碍物距离变化做出调整
2. **最小障碍物距离持续减少**：从1.65m减少到0.81m，最终发生碰撞
3. **算法输出固定**：总是选择相同的网格和速度向量

## 根本原因分析

通过详细调试发现了以下关键问题：

### 1. 方向偏好权重过大

**问题**：在原始的 `find_safest_grid_direction` 函数中，方向偏好（direction_bonus）的权重约为2.1，而安全性（safety）的权重只有0.33-1.0。这导致即使有障碍物，方向偏好仍然主导选择。

**表现**：
- 总分 = 安全性 + 方向偏好
- 即使安全性很低（0.33），方向偏好（2.1）仍然使前方中央网格得分最高
- 算法总是选择前方中央方向，无法避开障碍物

### 2. 紧急情况处理不当

**问题**：原始算法在检测到紧急情况时，虽然提高了安全性权重，但仍然只考虑前方5层网格，且没有有效的侧向避障策略。

**表现**：
- 总是选择网格[8,4,0]或[8,5,0]（都是第0层的中央网格）
- 目标位置总是 `(1.23, 0.66, 0.94)` 这样的近距离位置
- 无人机只是在原地打转，而不是真正避开障碍物

## 修复方案

### 1. 重新平衡权重系统

```python
# 修复前
safety_weight = 10.0  # 过高的权重
direction_bonus += (grid_d_bins - d) / grid_d_bins * 0.2  # 固定的方向偏好

# 修复后
safety_weight = 1.0  # 合理的权重
# 根据情况调整方向偏好
if is_emergency:
    forward_preference = 0.1  # 减少前方偏好
    side_preference = 2.0     # 增加侧向偏好
else:
    forward_preference = 0.5  # 正常前方偏好
    side_preference = 0.2     # 正常侧向偏好
```

### 2. 改进紧急情况处理

```python
# 修复前
search_layers = range(min(5, grid_d_bins))  # 只搜索前5层

# 修复后
if is_emergency:
    search_layers = range(grid_d_bins)  # 搜索所有层，包括后方
    # 偏向左右两侧，避开中央
    h_side_bonus = abs(h - h_center) / max(h_center, 1) * side_preference
else:
    search_layers = range(min(5, grid_d_bins))  # 正常情况只搜索前方
```

### 3. 添加安全性过滤

```python
# 跳过安全性太低的网格
if safety < 0.1:
    continue
```

## 修复效果

### 修复前
- 紧急情况下选择网格：[8, 4, 0]
- 目标位置：(1.23, 0.66, 0.94)
- 速度向量：Vx=0.73, Vy=0.39, Vz=0.56
- 转向角：28.18°（几乎直行）

### 修复后
- 紧急情况下选择网格：[0, 4, 0]
- 目标位置：(1.23, -9.84, 0.94)
- 速度向量：Vx=0.123, Vy=-0.988, Vz=0.094
- 转向角：-82.91°（大幅左转避障）

## 关键改进点

1. **动态权重调整**：根据是否为紧急情况动态调整各种偏好的权重
2. **全方位搜索**：紧急情况下搜索所有方向，包括侧向和后方
3. **侧向偏好**：紧急情况下强烈偏向侧向移动，避开前方障碍物
4. **安全性过滤**：过滤掉安全性太低的网格，确保选择真正安全的路径

## 测试验证

通过调试脚本验证：
- 算法能够正确检测紧急情况
- 在紧急情况下选择完全不同的避障方向
- 转向角从28°变为-83°，实现了有效的避障行为

## 建议

1. **进一步测试**：在实际AirSim环境中测试修复后的避障效果
2. **参数调优**：根据实际测试结果微调各种权重参数
3. **添加更多调试信息**：增加更详细的调试输出，便于进一步优化
4. **考虑动态障碍物**：未来可以考虑处理动态障碍物的情况

## 文件修改

主要修改文件：`environment/depth_utils.py`
- 修改了 `find_safest_grid_direction` 函数
- 改进了紧急情况检测和处理逻辑
- 重新平衡了安全性和方向偏好的权重
