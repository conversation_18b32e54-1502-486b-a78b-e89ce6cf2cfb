#!/usr/bin/env python3
"""
调试实际运行时的数据
"""
import os
import time
import numpy as np
import matplotlib.pyplot as plt
from environment.drone_env import DroneEnvironment
from models.teacher_model import TeacherModel
from config import MIN_OBSTACLE_DISTANCE, FORWARD_SPEED

def debug_real_avoidance():
    """
    调试实际避障过程
    """
    print("=== 调试实际避障过程 ===")
    
    # 创建环境和模型
    env = DroneEnvironment(
        enable_collision_detection=False,  # 禁用碰撞检测以便调试
        min_obstacle_distance=MIN_OBSTACLE_DISTANCE
    )
    teacher = TeacherModel(min_distance=MIN_OBSTACLE_DISTANCE)
    
    try:
        # 重置环境
        print("重置环境...")
        observation = env.reset()
        
        print(f"初始观测形状: {observation.shape}")
        print(f"初始观测数据类型: {observation.dtype}")
        print(f"初始观测范围: [{np.min(observation):.2f}, {np.max(observation):.2f}]")
        
        # 保存初始深度图像
        plt.figure(figsize=(12, 8))
        plt.subplot(2, 2, 1)
        plt.imshow(observation, cmap='viridis')
        plt.title('初始深度图像')
        plt.colorbar()
        
        # 运行几步并分析数据
        for step in range(5):
            print(f"\n--- Step {step + 1} ---")
            
            # 获取当前高度
            current_height = env.get_current_height()
            print(f"当前高度: {current_height:.2f}m")
            
            # 处理深度图像
            result = teacher.process_depth_image(observation, current_height)
            grid_3d = result["grid_3d"]
            min_distance = result["min_distance"]
            
            print(f"3D网格形状: {grid_3d.shape}")
            print(f"3D网格最小距离: {min_distance:.2f}m")
            print(f"3D网格范围: [{np.min(grid_3d):.2f}, {np.max(grid_3d):.2f}]")
            
            # 统计网格中不同距离范围的数量
            close_count = np.sum(grid_3d < 3.0)
            medium_count = np.sum((grid_3d >= 3.0) & (grid_3d < 8.0))
            far_count = np.sum(grid_3d >= 8.0)
            
            print(f"网格统计: 近距离(<3m): {close_count}, 中距离(3-8m): {medium_count}, 远距离(>=8m): {far_count}")
            
            # 获取速度向量
            velocity_vector = teacher.get_velocity_vector(observation, current_height)
            print(f"速度向量: Vx={velocity_vector[0]:.3f}, Vy={velocity_vector[1]:.3f}, Vz={velocity_vector[2]:.3f}")
            
            # 计算转向角
            if abs(velocity_vector[0]) > 0.01:
                turn_angle = np.degrees(np.arctan2(velocity_vector[1], velocity_vector[0]))
                print(f"等效转向角: {turn_angle:.2f}度")
            
            # 可视化当前步骤的数据
            if step < 3:  # 只可视化前3步
                plt.subplot(2, 2, step + 2)
                plt.imshow(observation, cmap='viridis')
                plt.title(f'Step {step + 1} 深度图像\n最小距离: {min_distance:.2f}m')
                plt.colorbar()
            
            # 执行动作
            next_observation, reward, done, info = env.step(velocity_vector)
            
            print(f"奖励: {reward:.2f}")
            print(f"完成: {done}")
            
            # 更新观测
            observation = next_observation
            
            # 如果完成，退出
            if done:
                print("回合结束")
                break
            
            # 等待一小段时间
            time.sleep(1.0)
        
        # 保存可视化结果
        plt.tight_layout()
        plt.savefig('debug_real_data_visualization.png', dpi=150, bbox_inches='tight')
        print("\n可视化结果已保存到 debug_real_data_visualization.png")
        
    except Exception as e:
        print(f"调试过程中出错: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 关闭环境
        env.close()

def analyze_point_cloud_conversion():
    """
    分析点云转换过程
    """
    print("\n=== 分析点云转换过程 ===")
    
    # 创建一个简单的测试深度图像
    depth_img = np.full((72, 128), 15.0)  # 全部设为远距离
    
    # 在中央前方放置一个近距离障碍物
    center_h, center_w = 36, 64
    for h in range(center_h - 5, center_h + 6):
        for w in range(center_w - 10, center_w + 11):
            if 0 <= h < 72 and 0 <= w < 128:
                depth_img[h, w] = 3.0  # 3米距离的障碍物
    
    print(f"测试深度图像形状: {depth_img.shape}")
    print(f"测试深度图像范围: [{np.min(depth_img):.2f}, {np.max(depth_img):.2f}]")
    
    # 转换为点云
    from environment.depth_utils import depth_to_point_cloud, create_3d_grid_from_points
    from config import CAMERA_FOV_H, DEPTH_SCALE, GRID_HORIZONTAL_BINS, GRID_VERTICAL_BINS, GRID_DEPTH_BINS, MAX_DETECTION_RANGE
    
    points = depth_to_point_cloud(depth_img, CAMERA_FOV_H, DEPTH_SCALE)
    print(f"点云数量: {len(points)}")
    
    if len(points) > 0:
        print(f"点云X范围: [{np.min(points[:, 0]):.2f}, {np.max(points[:, 0]):.2f}]")
        print(f"点云Y范围: [{np.min(points[:, 1]):.2f}, {np.max(points[:, 1]):.2f}]")
        print(f"点云Z范围: [{np.min(points[:, 2]):.2f}, {np.max(points[:, 2]):.2f}]")
        
        # 统计不同距离的点
        distances = np.linalg.norm(points, axis=1)
        close_points = np.sum(distances < 5.0)
        far_points = np.sum(distances >= 5.0)
        print(f"近距离点(<5m): {close_points}, 远距离点(>=5m): {far_points}")
        
        # 创建3D网格
        grid_3d = create_3d_grid_from_points(
            points,
            GRID_HORIZONTAL_BINS,
            GRID_VERTICAL_BINS,
            GRID_DEPTH_BINS,
            MAX_DETECTION_RANGE
        )
        
        print(f"3D网格形状: {grid_3d.shape}")
        print(f"3D网格最小距离: {np.min(grid_3d):.2f}m")
        print(f"3D网格最大距离: {np.max(grid_3d):.2f}m")
        
        # 统计网格
        close_grids = np.sum(grid_3d < 5.0)
        far_grids = np.sum(grid_3d >= 5.0)
        print(f"近距离网格(<5m): {close_grids}, 远距离网格(>=5m): {far_grids}")

if __name__ == "__main__":
    print("开始调试实际数据...")
    
    # 分析点云转换
    analyze_point_cloud_conversion()
    
    # 调试实际避障过程
    debug_real_avoidance()
    
    print("\n调试完成!")
