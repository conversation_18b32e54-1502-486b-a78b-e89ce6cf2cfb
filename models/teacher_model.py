"""
教师模型（规则算法）
"""
import numpy as np
from typing import Dict, Any

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config import (
    MIN_OBSTACLE_DISTANCE, MAX_TURN_ANGLE, FORWARD_SPEED,
    CAMERA_FOV_H, DEPTH_SCALE, MAX_DEPTH, SKY_DEPTH,
    HEIGHT_WARNING_THRESHOLD, HEIGHT_RESTRICTION_RATIO, DEPTH_HEIGHT,
    GRID_HORIZONTAL_BINS, GRID_VERTICAL_BINS, GRID_DEPTH_BINS, MAX_DETECTION_RANGE
)
from environment.depth_utils import (
    process_depth_image, depth_to_point_cloud,
    create_3d_grid_from_points, find_safest_grid_direction
)

class TeacherModel:
    """
    基于规则的避障算法，作为教师模型
    """
    def __init__(self, min_distance: float = MIN_OBSTACLE_DISTANCE, max_angle: float = MAX_TURN_ANGLE):
        """
        初始化教师模型

        Args:
            min_distance: 最小安全距离
            max_angle: 最大转向角
        """
        self.min_distance = min_distance
        self.max_angle = max_angle

        # 缓存变量
        self.last_depth_image = None
        self.last_point_cloud = None
        self.last_3d_grid = None  # 缓存3D网格

    def apply_height_restriction(self, depth_img: np.ndarray, current_height: float) -> np.ndarray:
        """
        应用高度限制，当高度超过警戒阈值时，将深度图像上部区域设为高风险

        Args:
            depth_img: 深度图像
            current_height: 当前高度

        Returns:
            处理后的深度图像
        """
        if current_height > HEIGHT_WARNING_THRESHOLD:
            # 计算需要限制的上部区域
            restricted_height = int(DEPTH_HEIGHT * (1 - HEIGHT_RESTRICTION_RATIO))

            # 将上部区域的深度值设为最小值，表示高风险
            modified_depth = depth_img.copy()
            modified_depth[:restricted_height, :] = 0.1  # 设为很小的深度值，表示极高风险

            print(f"[高度限制] 当前高度: {current_height:.2f}m > 警戒高度: {HEIGHT_WARNING_THRESHOLD:.2f}m")
            print(f"[高度限制] 限制上部 {restricted_height} 行像素 (总共 {DEPTH_HEIGHT} 行)")

            return modified_depth

        return depth_img

    def process_depth_image(self, depth_img: np.ndarray, current_height: float = 0.0) -> Dict[str, Any]:
        """
        处理深度图像，提取避障所需的信息

        Args:
            depth_img: 深度图像
            current_height: 当前高度

        Returns:
            处理结果字典
        """
        # 应用高度限制
        height_restricted_depth = self.apply_height_restriction(depth_img, current_height)

        # 缓存深度图像
        self.last_depth_image = height_restricted_depth

        # 处理深度图像（处理无效值和天空）
        processed_depth = process_depth_image(height_restricted_depth, MAX_DEPTH, SKY_DEPTH)

        # 将深度图像转换为点云
        points = depth_to_point_cloud(processed_depth, CAMERA_FOV_H, DEPTH_SCALE)

        # 缓存点云
        self.last_point_cloud = points

        # 如果没有点，返回空结果
        if len(points) == 0:
            # 创建空的3D网格
            empty_grid = np.full((GRID_HORIZONTAL_BINS, GRID_VERTICAL_BINS, GRID_DEPTH_BINS), MAX_DETECTION_RANGE)
            self.last_3d_grid = empty_grid
            return {
                "min_distance": MAX_DEPTH,
                "grid_3d": empty_grid
            }

        # 创建3D网格
        grid_3d = create_3d_grid_from_points(
            points,
            GRID_HORIZONTAL_BINS,
            GRID_VERTICAL_BINS,
            GRID_DEPTH_BINS,
            MAX_DETECTION_RANGE
        )

        # 缓存3D网格
        self.last_3d_grid = grid_3d

        # 计算最小距离
        min_distance = np.min(grid_3d)

        return {
            "min_distance": min_distance,
            "grid_3d": grid_3d
        }



    def get_velocity_vector(self, observation: np.ndarray, current_height: float = 0.0) -> np.ndarray:
        """
        获取三维速度向量 (Vx, Vy, Vz) - 使用3D网格避障

        Args:
            observation: 深度图像或距离数组
            current_height: 当前高度

        Returns:
            三维速度向量 [Vx, Vy, Vz] (相对机体坐标系)
        """
        # 检查输入类型
        if len(observation.shape) == 2:
            # 输入是深度图像，需要传入高度信息
            result = self.process_depth_image(observation, current_height)
            grid_3d = result["grid_3d"]
            min_distance = result["min_distance"]

            # 打印调试信息
            print(f"[3D避障调试] 最小障碍物距离: {min_distance:.2f}m")
            print(f"[3D避障调试] 当前高度: {current_height:.2f}m")
            print(f"[3D避障调试] 3D网格形状: {grid_3d.shape}")
        else:
            # 输入是距离数组，使用缓存的3D网格
            if self.last_3d_grid is not None:
                grid_3d = self.last_3d_grid
                min_distance = np.min(observation)
                print(f"[3D避障调试] 使用缓存的3D网格，最小距离: {min_distance:.2f}m")
            else:
                # 如果没有3D网格，返回紧急避障策略
                print("[3D避障调试] 没有3D网格，使用紧急避障策略")
                emergency_velocity = np.array([FORWARD_SPEED * 0.5, 0.0, 0.0])
                return emergency_velocity

        # 使用新的网格安全性评估方法
        best_velocity = find_safest_grid_direction(
            grid_3d,
            base_speed=FORWARD_SPEED,
            min_distance=self.min_distance,
            max_range=MAX_DETECTION_RANGE
        )

        print(f"[3D避障调试] 最佳速度向量: Vx={best_velocity[0]:.2f}, Vy={best_velocity[1]:.2f}, Vz={best_velocity[2]:.2f}")
        print(f"[3D避障调试] 总速度: {np.linalg.norm(best_velocity):.2f}m/s")

        # 检查速度向量的合理性
        speed_magnitude = np.linalg.norm(best_velocity)
        if speed_magnitude < FORWARD_SPEED * 0.1:
            print("[3D避障调试] 速度过小，采用紧急避障策略")
            # 紧急情况：减速并尝试上升
            emergency_velocity = np.array([FORWARD_SPEED * 0.3, 0.0, -FORWARD_SPEED * 0.2])
            print(f"[3D避障调试] 紧急速度向量: Vx={emergency_velocity[0]:.2f}, Vy={emergency_velocity[1]:.2f}, Vz={emergency_velocity[2]:.2f}")
            return emergency_velocity

        return best_velocity
