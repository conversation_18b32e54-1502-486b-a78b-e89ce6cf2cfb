"""
收集训练数据

默认情况下，只保存最终的数据文件（final.npz），不保存每个回合的中间数据文件。
如果需要保存每个回合的数据，可以使用--save_episodes参数。
如果需要可视化数据收集过程，可以使用--visualize参数。
"""
import os
import argparse

from environment.drone_env import DroneEnvironment
from models.teacher_model import TeacherModel
from training.data_collector import DataCollector
from config import DATA_COLLECTION_EPISODES, DATA_COLLECTION_STEPS_PER_EPISODE, DATA_DIR

def parse_args():
    """
    解析命令行参数
    """
    parser = argparse.ArgumentParser(description="Collect training data using teacher model")
    parser.add_argument("--episodes", type=int, default=DATA_COLLECTION_EPISODES, help="Number of episodes to collect")
    parser.add_argument("--steps", type=int, default=DATA_COLLECTION_STEPS_PER_EPISODE, help="Maximum steps per episode")
    parser.add_argument("--data_dir", type=str, default=DATA_DIR, help="Directory to save data")
    parser.add_argument("--save_episodes", action="store_true", help="Save data for each episode (default: False, only save final data)")

    return parser.parse_args()

def main():
    """
    主函数
    """
    # 解析命令行参数
    args = parse_args()

    # 创建保存目录
    os.makedirs(args.data_dir, exist_ok=True)

    # 创建环境和模型
    env = DroneEnvironment()
    teacher = TeacherModel()

    # 创建数据收集器
    collector = DataCollector(env, teacher, args.data_dir, args.save_episodes)

    try:
        # 收集数据
        observations, actions = collector.collect_data(args.episodes, args.steps)

        # 保存数据
        collector.save_data("final")

        print(f"Data collection completed: {len(observations)} samples collected")

    except KeyboardInterrupt:
        print("Interrupted by user")

        # 保存已收集的数据
        if len(collector.observations) > 0:
            collector.save_data("interrupted")

    finally:
        # 关闭环境
        env.close()

if __name__ == "__main__":
    main()
