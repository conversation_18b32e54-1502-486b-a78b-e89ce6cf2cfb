"""
雷达数据处理工具
"""
import numpy as np
import math
from typing import List, <PERSON><PERSON>

def process_lidar_data(lidar_data: List[float], num_points: int = 72) -> np.ndarray:
    """
    处理雷达数据，将原始数据转换为等间隔的点

    Args:
        lidar_data: 原始雷达数据
        num_points: 需要的点数量

    Returns:
        等间隔的雷达数据，numpy数组
    """
    # 确保数据长度正确
    if len(lidar_data) != num_points:
        raise ValueError(f"Expected {num_points} lidar points, got {len(lidar_data)}")

    # 转换为numpy数组
    return np.array(lidar_data)

def get_front_lidar_data(lidar_data: np.ndarray, num_points: int = 72) -> np.ndarray:
    """
    获取前方180度的雷达数据

    Args:
        lidar_data: 完整的雷达数据
        num_points: 总点数

    Returns:
        前方180度的雷达数据
    """
    # 计算前方180度对应的点数
    front_points = num_points // 2

    # 获取前方180度的数据（从-90度到90度）
    # 假设lidar_data的索引0对应0度（正前方）
    start_idx = num_points - front_points // 2
    end_idx = front_points // 2 + 1

    # 处理环绕情况
    front_data = np.concatenate([
        lidar_data[start_idx:],
        lidar_data[:end_idx]
    ])

    return front_data

def calculate_turn_angle(lidar_data: np.ndarray, min_distance: float, max_angle: float = 90.0) -> float:
    """
    根据雷达数据计算转向角

    Args:
        lidar_data: 前方180度的雷达数据
        min_distance: 最小安全距离
        max_angle: 最大转向角

    Returns:
        转向角（度）
    """
    # 找到最近的障碍物
    min_dist_idx = np.argmin(lidar_data)
    min_dist = lidar_data[min_dist_idx]

    # 如果没有障碍物或障碍物距离足够远，不需要转向
    if min_dist > min_distance:
        return 0.0

    # 计算障碍物的角度（相对于正前方）
    # 假设索引0对应-90度，索引len(lidar_data)-1对应90度
    angle_step = 180.0 / (len(lidar_data) - 1)
    obstacle_angle = -90.0 + min_dist_idx * angle_step

    # 计算转向角（远离障碍物）
    # 如果障碍物在左边，向右转；如果在右边，向左转
    turn_direction = -1 if obstacle_angle > 0 else 1

    # 转向角度与障碍物距离成反比，与障碍物角度成正比
    turn_angle = turn_direction * max_angle * (1.0 - min(min_dist / min_distance, 1.0)) * abs(obstacle_angle) / 90.0

    # 限制转向角在[-max_angle, max_angle]范围内
    return np.clip(turn_angle, -max_angle, max_angle)
