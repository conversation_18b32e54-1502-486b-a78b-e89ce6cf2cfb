#!/usr/bin/env python3
"""
快速测试避障算法
"""
import numpy as np
from environment.depth_utils import find_safest_grid_direction
from config import GRID_HORIZONTAL_BINS, GRID_VERTICAL_BINS, GRID_DEPTH_BINS, MAX_DETECTION_RANGE, MIN_OBSTACLE_DISTANCE, FORWARD_SPEED

def test_left_obstacle():
    """测试左侧障碍物"""
    print("=== 测试左侧障碍物 ===")
    grid_3d = np.full((GRID_HORIZONTAL_BINS, GRID_VERTICAL_BINS, GRID_DEPTH_BINS), MAX_DETECTION_RANGE)
    
    # 在左侧放置障碍物
    for d in range(3):
        for h in range(0, 8):  # 左侧
            for v in range(2, 7):
                grid_3d[h, v, d] = 1.0  # 1米距离
    
    velocity = find_safest_grid_direction(grid_3d, FORWARD_SPEED, MIN_OBSTACLE_DISTANCE, MAX_DETECTION_RANGE)
    angle = np.degrees(np.arctan2(velocity[1], velocity[0]))
    print(f"速度向量: {velocity}")
    print(f"转向角: {angle:.1f}度")
    return angle

def test_right_obstacle():
    """测试右侧障碍物"""
    print("\n=== 测试右侧障碍物 ===")
    grid_3d = np.full((GRID_HORIZONTAL_BINS, GRID_VERTICAL_BINS, GRID_DEPTH_BINS), MAX_DETECTION_RANGE)
    
    # 在右侧放置障碍物
    for d in range(3):
        for h in range(8, 16):  # 右侧
            for v in range(2, 7):
                grid_3d[h, v, d] = 1.0  # 1米距离
    
    velocity = find_safest_grid_direction(grid_3d, FORWARD_SPEED, MIN_OBSTACLE_DISTANCE, MAX_DETECTION_RANGE)
    angle = np.degrees(np.arctan2(velocity[1], velocity[0]))
    print(f"速度向量: {velocity}")
    print(f"转向角: {angle:.1f}度")
    return angle

if __name__ == "__main__":
    left_angle = test_left_obstacle()
    right_angle = test_right_obstacle()
    
    print(f"\n=== 结果 ===")
    print(f"左侧障碍物 -> {left_angle:.1f}度")
    print(f"右侧障碍物 -> {right_angle:.1f}度")
    
    if left_angle > 0 and right_angle < 0:
        print("✅ 算法能够智能选择转向方向")
    else:
        print("❌ 算法没有智能选择转向方向")
