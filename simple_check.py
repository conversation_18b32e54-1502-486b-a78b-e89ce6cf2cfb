"""
简单检查pickle文件
"""
import pickle

def main():
    # 文件路径
    episode_file = "data/lidar_data_20250514_174012_episode_99.pkl"
    final_file = "data/lidar_data_20250514_174017_final.pkl"
    
    # 加载数据
    with open(episode_file, 'rb') as f:
        episode_data = pickle.load(f)
    
    with open(final_file, 'rb') as f:
        final_data = pickle.load(f)
    
    # 打印基本信息
    print(f"Episode 99文件中的观测数量: {len(episode_data['observations'])}")
    print(f"Final文件中的观测数量: {len(final_data['observations'])}")
    
    # 检查是否相同
    same_length = len(episode_data['observations']) == len(final_data['observations'])
    print(f"两个文件的观测数量是否相同: {same_length}")
    
    # 检查前几个元素
    if same_length:
        same_first_element = (episode_data['observations'][0] == final_data['observations'][0]).all()
        print(f"第一个观测是否相同: {same_first_element}")
        
        same_last_element = (episode_data['observations'][-1] == final_data['observations'][-1]).all()
        print(f"最后一个观测是否相同: {same_last_element}")

if __name__ == "__main__":
    main()
