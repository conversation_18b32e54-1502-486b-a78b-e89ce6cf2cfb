#!/usr/bin/env python3
"""
测试方向改变逻辑
"""
import numpy as np
from environment.depth_utils import find_safest_grid_direction
from config import GRID_HORIZONTAL_BINS, GRID_VERTICAL_BINS, GRID_DEPTH_BINS, MAX_DETECTION_RANGE, MIN_OBSTACLE_DISTANCE, FORWARD_SPEED

def test_direction_change():
    """测试在安全性差异很大时能否改变方向"""
    print("=== 测试方向改变逻辑 ===")
    
    # 第一阶段：左侧有障碍物，应该选择右侧
    print("\n第一阶段：左侧有障碍物")
    grid_3d_1 = np.full((GRID_HORIZONTAL_BINS, GRID_VERTICAL_BINS, GRID_DEPTH_BINS), MAX_DETECTION_RANGE)
    for d in range(3):
        for h in range(0, 8):  # 左侧
            for v in range(2, 7):
                grid_3d_1[h, v, d] = 1.0
    
    # 第二阶段：右侧有障碍物，应该选择左侧
    print("\n第二阶段：右侧有障碍物")
    grid_3d_2 = np.full((GRID_HORIZONTAL_BINS, GRID_VERTICAL_BINS, GRID_DEPTH_BINS), MAX_DETECTION_RANGE)
    for d in range(3):
        for h in range(8, 16):  # 右侧
            for v in range(2, 7):
                grid_3d_2[h, v, d] = 1.0
    
    last_direction = None
    direction_steps = 0
    
    # 前3步：左侧有障碍物
    for step in range(3):
        print(f"\n--- Step {step + 1} (左侧障碍物) ---")
        
        result = find_safest_grid_direction(
            grid_3d_1, FORWARD_SPEED, MIN_OBSTACLE_DISTANCE, MAX_DETECTION_RANGE,
            last_direction, direction_steps
        )
        
        velocity, current_direction, new_direction_steps, safety_diff = result
        angle = np.degrees(np.arctan2(velocity[1], velocity[0]))
        
        print(f"转向角: {angle:.1f}度, 方向: {current_direction}, 步数: {new_direction_steps}")
        
        last_direction = current_direction
        direction_steps = new_direction_steps
    
    # 后3步：右侧有障碍物（测试是否能改变方向）
    for step in range(3):
        print(f"\n--- Step {step + 4} (右侧障碍物) ---")
        
        result = find_safest_grid_direction(
            grid_3d_2, FORWARD_SPEED, MIN_OBSTACLE_DISTANCE, MAX_DETECTION_RANGE,
            last_direction, direction_steps
        )
        
        velocity, current_direction, new_direction_steps, safety_diff = result
        angle = np.degrees(np.arctan2(velocity[1], velocity[0]))
        
        print(f"转向角: {angle:.1f}度, 方向: {current_direction}, 步数: {new_direction_steps}")
        
        last_direction = current_direction
        direction_steps = new_direction_steps

if __name__ == "__main__":
    test_direction_change()
