"""
学生模型（神经网络）
"""
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import os
from typing import Tuple, List, Dict, Any

import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config import (
    INPUT_CHANNELS, INPUT_WIDTH, INPUT_HEIGHT, HEIGHT_INPUT_SIZE,
    CONV_FILTERS, CONV_KERNEL_SIZES, CONV_STRIDES,
    FC_SIZES, OUTPUT_SIZE, LEARNING_RATE, BATCH_SIZE, EPOCHS, MODEL_DIR
)

class ObstacleAvoidanceCNN(nn.Module):
    """
    基于CNN+FC的避障神经网络模型
    """
    def __init__(self,
                 input_channels: int = INPUT_CHANNELS,
                 input_width: int = INPUT_WIDTH,
                 input_height: int = INPUT_HEIGHT,
                 height_input_size: int = HEIGHT_INPUT_SIZE,
                 conv_filters: List[int] = CONV_FILTERS,
                 conv_kernel_sizes: List[int] = CONV_KERNEL_SIZES,
                 conv_strides: List[int] = CONV_STRIDES,
                 fc_sizes: List[int] = FC_SIZES,
                 output_size: int = OUTPUT_SIZE):
        """
        初始化神经网络

        Args:
            input_channels: 输入通道数
            input_width: 输入宽度
            input_height: 输入高度
            height_input_size: 高度输入维度
            conv_filters: 卷积层滤波器数量
            conv_kernel_sizes: 卷积核大小
            conv_strides: 卷积步长
            fc_sizes: 全连接层大小
            output_size: 输出大小（三维速度向量）
        """
        super(ObstacleAvoidanceCNN, self).__init__()

        # 卷积层
        self.conv_layers = nn.ModuleList()
        in_channels = input_channels

        # 计算卷积后的特征图大小
        feature_width = input_width
        feature_height = input_height

        for i in range(len(conv_filters)):
            out_channels = conv_filters[i]
            kernel_size = conv_kernel_sizes[i]
            stride = conv_strides[i]

            self.conv_layers.append(nn.Conv2d(in_channels, out_channels, kernel_size, stride, padding=kernel_size//2))
            self.conv_layers.append(nn.ReLU())
            self.conv_layers.append(nn.BatchNorm2d(out_channels))

            # 更新特征图大小
            feature_width = (feature_width + 2*(kernel_size//2) - kernel_size) // stride + 1
            feature_height = (feature_height + 2*(kernel_size//2) - kernel_size) // stride + 1

            in_channels = out_channels

        # 计算展平后的特征数量
        flattened_size = feature_width * feature_height * conv_filters[-1]

        # 高度输入处理层
        self.height_fc = nn.Linear(height_input_size, 32)  # 将高度映射到32维特征

        # 全连接层
        self.fc_layers = nn.ModuleList()
        # 输入特征 = 卷积特征 + 高度特征
        in_features = flattened_size + 32

        for fc_size in fc_sizes:
            self.fc_layers.append(nn.Linear(in_features, fc_size))
            self.fc_layers.append(nn.ReLU())
            self.fc_layers.append(nn.Dropout(0.2))
            in_features = fc_size

        # 输出层
        self.output_layer = nn.Linear(in_features, output_size)

        # 保存特征图大小，用于前向传播
        self.feature_width = feature_width
        self.feature_height = feature_height
        self.final_conv_filters = conv_filters[-1]

    def forward(self, depth_img: torch.Tensor, height: torch.Tensor) -> torch.Tensor:
        """
        前向传播

        Args:
            depth_img: 深度图像张量 [batch_size, channels, height, width]
            height: 高度张量 [batch_size, 1]

        Returns:
            输出张量 [batch_size, output_size] (三维速度向量)
        """
        # 处理深度图像 - 卷积层
        x = depth_img
        for layer in self.conv_layers:
            x = layer(x)

        # 展平卷积特征
        conv_features = x.view(x.size(0), -1)

        # 处理高度输入
        height_features = torch.relu(self.height_fc(height))

        # 拼接卷积特征和高度特征
        combined_features = torch.cat([conv_features, height_features], dim=1)

        # 全连接层
        x = combined_features
        for layer in self.fc_layers:
            x = layer(x)

        # 输出层
        x = self.output_layer(x)

        return x

class DepthDataset(Dataset):
    """
    深度图像+高度数据集
    """
    def __init__(self, depth_images: np.ndarray, heights: np.ndarray, actions: np.ndarray):
        """
        初始化数据集

        Args:
            depth_images: 深度图像数据
            heights: 高度数据
            actions: 动作数据（三维速度向量）
        """
        # 确保深度图像形状正确
        if len(depth_images.shape) == 3:
            # 如果已经是3D数组 [samples, height, width]
            self.depth_images = depth_images
        elif len(depth_images.shape) == 4 and depth_images.shape[1] == 1:
            # 如果是4D数组 [samples, channels, height, width]
            self.depth_images = depth_images[:, 0, :, :]
        else:
            raise ValueError(f"Unexpected depth image shape: {depth_images.shape}")

        # 确保高度数据形状正确
        if len(heights.shape) == 1:
            # 如果是1D数组 [samples]，转换为 [samples, 1]
            heights = heights.reshape(-1, 1)
        elif len(heights.shape) != 2 or heights.shape[1] != 1:
            raise ValueError(f"Unexpected height shape: {heights.shape}")

        # 确保动作数据形状正确
        if len(actions.shape) == 1:
            # 如果是1D数组，可能是旧格式的转向角，需要转换为3D速度向量
            raise ValueError("Actions must be 3D velocity vectors, not 1D turning angles")
        elif len(actions.shape) != 2 or actions.shape[1] != 3:
            raise ValueError(f"Unexpected action shape: {actions.shape}, expected [samples, 3]")

        # 转换为PyTorch张量
        # 添加通道维度 [samples, height, width] -> [samples, 1, height, width]
        self.depth_images = torch.FloatTensor(self.depth_images).unsqueeze(1)
        self.heights = torch.FloatTensor(heights)
        self.actions = torch.FloatTensor(actions)

    def __len__(self) -> int:
        return len(self.depth_images)

    def __getitem__(self, idx: int) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        return self.depth_images[idx], self.heights[idx], self.actions[idx]

class StudentModel:
    """
    学生模型
    """
    def __init__(self,
                 input_channels: int = INPUT_CHANNELS,
                 input_width: int = INPUT_WIDTH,
                 input_height: int = INPUT_HEIGHT,
                 height_input_size: int = HEIGHT_INPUT_SIZE,
                 conv_filters: List[int] = CONV_FILTERS,
                 conv_kernel_sizes: List[int] = CONV_KERNEL_SIZES,
                 conv_strides: List[int] = CONV_STRIDES,
                 fc_sizes: List[int] = FC_SIZES,
                 output_size: int = OUTPUT_SIZE):
        """
        初始化学生模型

        Args:
            input_channels: 输入通道数
            input_width: 输入宽度
            input_height: 输入高度
            height_input_size: 高度输入维度
            conv_filters: 卷积层滤波器数量
            conv_kernel_sizes: 卷积核大小
            conv_strides: 卷积步长
            fc_sizes: 全连接层大小
            output_size: 输出大小（三维速度向量）
        """
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.model = ObstacleAvoidanceCNN(
            input_channels, input_width, input_height, height_input_size,
            conv_filters, conv_kernel_sizes, conv_strides,
            fc_sizes, output_size
        ).to(self.device)
        self.optimizer = optim.Adam(self.model.parameters(), lr=LEARNING_RATE)
        self.criterion = nn.MSELoss()

        # 创建模型保存目录
        os.makedirs(MODEL_DIR, exist_ok=True)

        # 打印模型结构
        print(f"Model created on device: {self.device}")
        print(f"Input shape: [{input_channels}, {input_height}, {input_width}]")

        # 计算模型参数数量
        total_params = sum(p.numel() for p in self.model.parameters())
        print(f"Total parameters: {total_params:,}")

    def train(self, depth_images: np.ndarray, heights: np.ndarray, actions: np.ndarray, epochs: int = EPOCHS, batch_size: int = BATCH_SIZE) -> List[float]:
        """
        训练模型

        Args:
            depth_images: 深度图像数据
            heights: 高度数据
            actions: 动作数据（三维速度向量）
            epochs: 训练轮数
            batch_size: 批次大小

        Returns:
            训练损失历史
        """
        # 创建数据集和数据加载器
        dataset = DepthDataset(depth_images, heights, actions)
        dataloader = DataLoader(dataset, batch_size=batch_size, shuffle=True)

        # 训练模型
        self.model.train()
        loss_history = []

        for epoch in range(epochs):
            epoch_loss = 0.0
            for batch_depth_images, batch_heights, batch_actions in dataloader:
                batch_depth_images = batch_depth_images.to(self.device)
                batch_heights = batch_heights.to(self.device)
                batch_actions = batch_actions.to(self.device)

                # 前向传播
                self.optimizer.zero_grad()
                outputs = self.model(batch_depth_images, batch_heights)
                loss = self.criterion(outputs, batch_actions)

                # 反向传播
                loss.backward()
                self.optimizer.step()

                epoch_loss += loss.item()

            # 记录损失
            avg_loss = epoch_loss / len(dataloader)
            loss_history.append(avg_loss)

            # 打印进度
            if (epoch + 1) % 10 == 0:
                print(f"Epoch {epoch + 1}/{epochs}, Loss: {avg_loss:.6f}")

        return loss_history

    def predict(self, depth_image: np.ndarray, height: float) -> np.ndarray:
        """
        预测动作

        Args:
            depth_image: 深度图像数据
            height: 当前高度

        Returns:
            预测的三维速度向量 [Vx, Vy, Vz]
        """
        self.model.eval()
        with torch.no_grad():
            # 确保深度图像输入形状正确
            if len(depth_image.shape) == 2:
                # 单张深度图像 [height, width]
                # 转换为 [1, 1, height, width]
                depth_tensor = torch.FloatTensor(depth_image).unsqueeze(0).unsqueeze(0).to(self.device)
            elif len(depth_image.shape) == 3 and depth_image.shape[0] == 1:
                # 已经有通道维度 [1, height, width]
                depth_tensor = torch.FloatTensor(depth_image).unsqueeze(0).to(self.device)
            else:
                raise ValueError(f"Unexpected depth image shape: {depth_image.shape}")

            # 处理高度输入
            height_tensor = torch.FloatTensor([[height]]).to(self.device)

            output = self.model(depth_tensor, height_tensor)
            return output.cpu().numpy().flatten()  # 返回三维速度向量

    def save(self, path: str = None) -> None:
        """
        保存模型

        Args:
            path: 保存路径
        """
        if path is None:
            path = os.path.join(MODEL_DIR, "student_model.pth")

        torch.save({
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'model_config': {
                'input_channels': INPUT_CHANNELS,
                'input_width': INPUT_WIDTH,
                'input_height': INPUT_HEIGHT,
                'height_input_size': HEIGHT_INPUT_SIZE,
                'conv_filters': CONV_FILTERS,
                'conv_kernel_sizes': CONV_KERNEL_SIZES,
                'conv_strides': CONV_STRIDES,
                'fc_sizes': FC_SIZES,
                'output_size': OUTPUT_SIZE
            }
        }, path)

        print(f"Model saved to {path}")

    def load(self, path: str) -> None:
        """
        加载模型

        Args:
            path: 加载路径
        """
        checkpoint = torch.load(path, map_location=self.device)

        # 检查是否有模型配置
        if 'model_config' in checkpoint:
            config = checkpoint['model_config']
            # 使用保存的配置重新创建模型
            self.model = ObstacleAvoidanceCNN(
                config['input_channels'],
                config['input_width'],
                config['input_height'],
                config.get('height_input_size', HEIGHT_INPUT_SIZE),  # 向后兼容
                config['conv_filters'],
                config['conv_kernel_sizes'],
                config['conv_strides'],
                config['fc_sizes'],
                config['output_size']
            ).to(self.device)

        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])

        print(f"Model loaded from {path}")
