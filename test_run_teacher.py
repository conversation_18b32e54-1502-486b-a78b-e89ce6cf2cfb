#!/usr/bin/env python3
"""
测试run_teacher.py的核心功能（不需要AirSim连接）
"""
import numpy as np
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models.teacher_model import TeacherModel
from config import *

def test_teacher_3d_functionality():
    """测试教师模型的3D功能"""
    print("=== 测试教师模型3D功能 ===")
    
    # 创建教师模型
    teacher = TeacherModel()
    
    # 测试场景
    scenarios = [
        ("正常飞行", 3.0),
        ("高度警戒", 5.5),
        ("高度过高", 7.0),
        ("低空飞行", 1.5),
    ]
    
    for scenario_name, height in scenarios:
        print(f"\n--- {scenario_name} (高度: {height}m) ---")
        
        # 创建模拟深度图像
        depth_image = np.random.rand(DEPTH_HEIGHT, DEPTH_WIDTH) * 10 + 5
        
        # 在前方添加一些障碍物
        if "正常" not in scenario_name:
            # 在前方中央区域添加障碍物
            center_h = DEPTH_HEIGHT // 2
            center_w = DEPTH_WIDTH // 2
            depth_image[center_h-10:center_h+10, center_w-20:center_w+20] = 3.0
        
        try:
            # 获取3D速度向量
            velocity_vector = teacher.get_velocity_vector(depth_image, height)
            
            vx, vy, vz = velocity_vector
            total_speed = np.linalg.norm(velocity_vector)
            horizontal_speed = np.linalg.norm(velocity_vector[:2])
            
            print(f"输出速度向量: Vx={vx:.2f}, Vy={vy:.2f}, Vz={vz:.2f}")
            print(f"总速度: {total_speed:.2f}m/s")
            print(f"水平速度: {horizontal_speed:.2f}m/s")
            
            # 分析策略
            strategies = []
            if abs(vx) < 0.3:
                strategies.append("减速")
            elif vx > 0.8:
                strategies.append("正常前进")
                
            if abs(vy) > 0.3:
                direction = "右转" if vy > 0 else "左转"
                strategies.append(direction)
                
            if abs(vz) > 0.2:
                direction = "上升" if vz < 0 else "下降"
                strategies.append(direction)
                
            if not strategies:
                strategies.append("直行")
                
            print(f"避障策略: {', '.join(strategies)}")
            
            # 验证高度限制
            if height > HEIGHT_WARNING_THRESHOLD:
                print(f"✓ 高度限制已触发 (当前: {height}m > 警戒: {HEIGHT_WARNING_THRESHOLD}m)")
                if abs(vz) > 0.1:
                    print(f"✓ 检测到垂直机动: Vz={vz:.2f}")
            
            print("✓ 测试成功")
            
        except Exception as e:
            print(f"✗ 测试失败: {e}")
            import traceback
            traceback.print_exc()

def test_3d_grid_integration():
    """测试3D网格集成"""
    print("\n=== 测试3D网格集成 ===")
    
    teacher = TeacherModel()
    
    # 创建包含明显障碍物的深度图像
    depth_image = np.full((DEPTH_HEIGHT, DEPTH_WIDTH), 15.0)  # 远距离背景
    
    # 在前方中央添加近距离障碍物
    center_h = DEPTH_HEIGHT // 2
    center_w = DEPTH_WIDTH // 2
    depth_image[center_h-15:center_h+15, center_w-30:center_w+30] = 2.0  # 2米距离的障碍物
    
    # 在左侧添加障碍物
    depth_image[center_h-10:center_h+10, center_w-60:center_w-30] = 3.0  # 3米距离
    
    # 在上方添加障碍物（如果不是高度限制场景）
    depth_image[center_h-30:center_h-15, center_w-15:center_w+15] = 4.0  # 4米距离
    
    print("创建了复杂障碍物场景:")
    print(f"- 前方中央: 2米距离障碍物")
    print(f"- 左侧: 3米距离障碍物") 
    print(f"- 上方: 4米距离障碍物")
    
    # 测试不同高度
    test_heights = [2.0, 4.0, 6.0]
    
    for height in test_heights:
        print(f"\n--- 高度: {height}m ---")
        
        velocity_vector = teacher.get_velocity_vector(depth_image, height)
        vx, vy, vz = velocity_vector
        
        print(f"速度向量: Vx={vx:.2f}, Vy={vy:.2f}, Vz={vz:.2f}")
        
        # 分析避障决策
        if abs(vy) > 0.3:
            print(f"✓ 检测到侧向避障: {'右转' if vy > 0 else '左转'}")
        if abs(vz) > 0.2:
            print(f"✓ 检测到垂直避障: {'上升' if vz < 0 else '下降'}")
        if vx < 0.5:
            print(f"✓ 检测到减速策略")
            
        # 验证3D网格缓存
        if teacher.last_3d_grid is not None:
            grid_shape = teacher.last_3d_grid.shape
            min_distance = np.min(teacher.last_3d_grid)
            print(f"✓ 3D网格已生成: {grid_shape}, 最小距离: {min_distance:.2f}m")
        else:
            print("✗ 3D网格未生成")

def test_velocity_vector_properties():
    """测试速度向量的属性"""
    print("\n=== 测试速度向量属性 ===")
    
    teacher = TeacherModel()
    
    # 生成多个测试样本
    num_tests = 10
    velocities = []
    
    for i in range(num_tests):
        # 随机深度图像
        depth_image = np.random.rand(DEPTH_HEIGHT, DEPTH_WIDTH) * 15 + 2
        height = np.random.uniform(1.0, 8.0)
        
        velocity = teacher.get_velocity_vector(depth_image, height)
        velocities.append(velocity)
    
    velocities = np.array(velocities)
    
    # 分析速度向量属性
    vx_range = [np.min(velocities[:, 0]), np.max(velocities[:, 0])]
    vy_range = [np.min(velocities[:, 1]), np.max(velocities[:, 1])]
    vz_range = [np.min(velocities[:, 2]), np.max(velocities[:, 2])]
    
    print(f"Vx范围: [{vx_range[0]:.2f}, {vx_range[1]:.2f}]")
    print(f"Vy范围: [{vy_range[0]:.2f}, {vy_range[1]:.2f}]")
    print(f"Vz范围: [{vz_range[0]:.2f}, {vz_range[1]:.2f}]")
    
    # 检查是否有非零的Vz值
    non_zero_vz = np.sum(np.abs(velocities[:, 2]) > 0.1)
    print(f"包含垂直分量的样本: {non_zero_vz}/{num_tests}")
    
    if non_zero_vz > 0:
        print("✓ 系统支持垂直机动")
    else:
        print("⚠ 未检测到垂直机动，可能需要更复杂的场景")
    
    # 计算平均速度
    avg_speeds = [np.linalg.norm(v) for v in velocities]
    print(f"平均总速度: {np.mean(avg_speeds):.2f} ± {np.std(avg_speeds):.2f} m/s")

def main():
    """主测试函数"""
    print("🚁 测试run_teacher.py核心功能")
    print("="*60)
    
    tests = [
        ("教师模型3D功能", test_teacher_3d_functionality),
        ("3D网格集成", test_3d_grid_integration),
        ("速度向量属性", test_velocity_vector_properties),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        print(f"运行测试: {test_name}")
        print('='*60)
        
        try:
            test_func()
            passed += 1
            print(f"\n✓ {test_name} 测试通过")
        except Exception as e:
            print(f"\n✗ {test_name} 测试失败: {e}")
            import traceback
            traceback.print_exc()
    
    print(f"\n{'='*60}")
    print(f"测试结果: {passed}/{total} 通过")
    print('='*60)
    
    if passed == total:
        print("🎉 所有测试通过！run_teacher.py准备就绪。")
        print("\n📝 使用说明:")
        print("1. 确保AirSim正在运行")
        print("2. 运行: python3 run_teacher.py --max_steps 50")
        print("3. 观察3D避障效果")
        return True
    else:
        print("❌ 部分测试失败，请检查代码。")
        return False

if __name__ == "__main__":
    main()
