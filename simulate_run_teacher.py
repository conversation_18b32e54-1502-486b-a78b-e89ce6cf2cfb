#!/usr/bin/env python3
"""
模拟run_teacher.py的运行，展示新的避障效果
"""
import numpy as np
import sys
import os
import time

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from models.teacher_model import TeacherModel
from config import FORWARD_SPEED, MIN_OBSTACLE_DISTANCE

def simulate_flight_scenario():
    """模拟飞行场景"""
    print("=" * 60)
    print("模拟AirSim飞行场景 - 新的网格安全性评估方法")
    print("=" * 60)
    
    # 创建教师模型
    teacher = TeacherModel(min_distance=MIN_OBSTACLE_DISTANCE)
    print(f"✓ 教师模型初始化完成")
    print(f"  最小安全距离: {MIN_OBSTACLE_DISTANCE}m")
    print(f"  前进速度: {FORWARD_SPEED}m/s")
    print()
    
    # 模拟飞行步骤
    scenarios = [
        {
            "step": 1,
            "description": "起飞后，前方开阔",
            "depth": np.ones((72, 128)) * 15.0,
            "height": 1.5
        },
        {
            "step": 2,
            "description": "发现前方障碍物",
            "depth": create_obstacle_scene(center_obstacle=True),
            "height": 1.8
        },
        {
            "step": 3,
            "description": "左侧有障碍物，需要右转",
            "depth": create_obstacle_scene(left_obstacle=True),
            "height": 2.1
        },
        {
            "step": 4,
            "description": "复杂环境，多个障碍物",
            "depth": create_obstacle_scene(complex=True),
            "height": 2.4
        },
        {
            "step": 5,
            "description": "高度过高，需要下降",
            "depth": create_obstacle_scene(high_altitude=True),
            "height": 8.0
        }
    ]
    
    total_time = 0
    for scenario in scenarios:
        print(f"步骤 {scenario['step']}: {scenario['description']}")
        print(f"  当前高度: {scenario['height']:.1f}m")
        
        # 计算避障速度
        start_time = time.time()
        velocity_vector = teacher.get_velocity_vector(scenario['depth'], scenario['height'])
        computation_time = time.time() - start_time
        total_time += computation_time
        
        # 分析结果
        vx, vy, vz = velocity_vector
        speed = np.linalg.norm(velocity_vector)
        
        # 计算等效转向角（用于对比）
        if abs(vx) > 0.01:
            turn_angle = np.degrees(np.arctan2(vy, vx))
        else:
            turn_angle = 0.0
        
        print(f"  🎯 避障决策:")
        print(f"    速度向量: Vx={vx:.3f}, Vy={vy:.3f}, Vz={vz:.3f}")
        print(f"    总速度: {speed:.3f}m/s")
        print(f"    等效转向角: {turn_angle:.1f}°")
        print(f"    计算时间: {computation_time*1000:.1f}ms")
        
        # 分析飞行策略
        strategy = analyze_flight_strategy(vx, vy, vz)
        print(f"    飞行策略: {strategy}")
        
        # 验证速度
        if abs(speed - FORWARD_SPEED) < 0.01:
            print(f"    ✅ 速度控制正确")
        else:
            print(f"    ⚠️ 速度异常: {speed:.3f} (期望: {FORWARD_SPEED})")
        
        print("    " + "-" * 50)
    
    print(f"\n📊 性能统计:")
    print(f"  总计算时间: {total_time*1000:.1f}ms")
    print(f"  平均每步: {total_time/len(scenarios)*1000:.1f}ms")
    print(f"  处理频率: {len(scenarios)/total_time:.1f} Hz")
    
    print(f"\n🎉 模拟完成！新方法特点:")
    print(f"  ✅ 速度大小始终保持 {FORWARD_SPEED}m/s")
    print(f"  ✅ 计算效率高，平均 {total_time/len(scenarios)*1000:.1f}ms/步")
    print(f"  ✅ 结果稳定，相同输入产生相同输出")
    print(f"  ✅ 避障策略直接，基于网格安全性评估")

def create_obstacle_scene(center_obstacle=False, left_obstacle=False, 
                         right_obstacle=False, complex=False, high_altitude=False):
    """创建不同的障碍物场景"""
    depth_img = np.ones((72, 128)) * 15.0  # 默认15米距离
    
    if center_obstacle:
        # 前方中央有障碍物
        depth_img[30:45, 55:75] = 3.0
        
    elif left_obstacle:
        # 左侧有障碍物
        depth_img[25:50, 20:50] = 2.5
        
    elif right_obstacle:
        # 右侧有障碍物
        depth_img[25:50, 80:110] = 2.5
        
    elif complex:
        # 复杂环境
        depth_img[20:30, 30:50] = 4.0   # 左上
        depth_img[45:55, 80:100] = 3.5  # 右下
        depth_img[35:40, 55:75] = 2.8   # 中央
        
    elif high_altitude:
        # 高度过高场景
        depth_img[15:25, 60:70] = 5.0   # 上方有障碍物
    
    return depth_img

def analyze_flight_strategy(vx, vy, vz):
    """分析飞行策略"""
    strategy_parts = []
    
    # 前后方向
    if vx > 0.1:
        strategy_parts.append("前进")
    elif vx < -0.1:
        strategy_parts.append("后退")
    else:
        strategy_parts.append("悬停")
    
    # 左右方向
    if vy > 0.1:
        strategy_parts.append("右转")
    elif vy < -0.1:
        strategy_parts.append("左转")
    
    # 上下方向
    if vz > 0.1:
        strategy_parts.append("下降")
    elif vz < -0.1:
        strategy_parts.append("上升")
    
    return " + ".join(strategy_parts)

if __name__ == "__main__":
    simulate_flight_scenario()
