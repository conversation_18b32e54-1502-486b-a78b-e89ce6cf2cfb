#!/usr/bin/env python3
"""
测试方向稳定性
"""
import numpy as np
from environment.depth_utils import find_safest_grid_direction
from config import GRID_HORIZONTAL_BINS, GRID_VERTICAL_BINS, GRID_DEPTH_BINS, MAX_DETECTION_RANGE, MIN_OBSTACLE_DISTANCE, FORWARD_SPEED

def test_direction_stability():
    """测试方向稳定性"""
    print("=== 测试方向稳定性 ===")
    
    # 创建一个有左侧障碍物的网格
    grid_3d = np.full((GRID_HORIZONTAL_BINS, GRID_VERTICAL_BINS, GRID_DEPTH_BINS), MAX_DETECTION_RANGE)
    
    # 在左侧放置障碍物
    for d in range(3):
        for h in range(0, 8):  # 左侧
            for v in range(2, 7):
                grid_3d[h, v, d] = 1.0  # 1米距离
    
    # 模拟连续几步的决策
    last_direction = None
    direction_steps = 0
    
    for step in range(8):
        print(f"\n--- Step {step + 1} ---")
        
        result = find_safest_grid_direction(
            grid_3d, FORWARD_SPEED, MIN_OBSTACLE_DISTANCE, MAX_DETECTION_RANGE,
            last_direction, direction_steps
        )
        
        velocity, current_direction, new_direction_steps, safety_diff = result
        angle = np.degrees(np.arctan2(velocity[1], velocity[0]))
        
        print(f"速度向量: [{velocity[0]:.2f}, {velocity[1]:.2f}, {velocity[2]:.2f}]")
        print(f"转向角: {angle:.1f}度")
        print(f"当前方向: {current_direction}")
        print(f"方向步数: {new_direction_steps}")
        
        # 更新状态
        last_direction = current_direction
        direction_steps = new_direction_steps

if __name__ == "__main__":
    test_direction_stability()
