#!/usr/bin/env python3
"""
测试新的网格安全性评估方法
"""
import numpy as np
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_grid_safety():
    """测试网格安全性评估"""
    print("开始测试网格安全性评估方法...")
    
    try:
        # 导入必要的模块
        from environment.depth_utils import find_safest_grid_direction, create_3d_grid_from_points
        print("✓ 成功导入函数")
        
        # 创建一些测试点云数据
        points = np.array([
            [5.0, 0.0, 0.0],   # 正前方5米
            [3.0, 2.0, 0.0],   # 右前方3米
            [4.0, -1.0, 0.0],  # 左前方4米
            [8.0, 0.0, 1.0],   # 正前方上方8米
        ])
        print(f"✓ 创建测试点云，形状: {points.shape}")
        
        # 创建3D网格
        grid_3d = create_3d_grid_from_points(points, 16, 8, 10, 15.0)
        print(f"✓ 创建3D网格，形状: {grid_3d.shape}")
        print(f"  网格最小值: {np.min(grid_3d):.2f}")
        print(f"  网格最大值: {np.max(grid_3d):.2f}")
        
        # 测试找到最安全方向
        velocity = find_safest_grid_direction(grid_3d, base_speed=1.0)
        print(f"✓ 计算得到的速度向量: [{velocity[0]:.3f}, {velocity[1]:.3f}, {velocity[2]:.3f}]")
        print(f"  速度大小: {np.linalg.norm(velocity):.3f}")
        
        # 验证速度向量的合理性
        speed = np.linalg.norm(velocity)
        if abs(speed - 1.0) < 0.01:
            print("✓ 速度大小正确")
        else:
            print(f"⚠ 速度大小异常: {speed:.3f} (期望: 1.0)")
            
        print("✓ 网格安全性评估测试完成！")
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_teacher_model():
    """测试教师模型"""
    print("\n开始测试教师模型...")
    
    try:
        from models.teacher_model import TeacherModel
        print("✓ 成功导入TeacherModel")
        
        # 创建教师模型
        teacher = TeacherModel()
        print("✓ 成功创建教师模型实例")
        
        # 创建一个简单的测试深度图像
        depth_img = np.ones((72, 128)) * 10.0  # 10米距离
        depth_img[30:40, 60:70] = 2.0  # 在中央放置一个近距离障碍物
        print(f"✓ 创建测试深度图像，形状: {depth_img.shape}")
        
        # 测试新的速度向量计算
        velocity_vector = teacher.get_velocity_vector(depth_img, current_height=2.0)
        print(f"✓ 计算得到速度向量: [{velocity_vector[0]:.3f}, {velocity_vector[1]:.3f}, {velocity_vector[2]:.3f}]")
        print(f"  速度大小: {np.linalg.norm(velocity_vector):.3f}")
        
        print("✓ 教师模型测试完成！")
        return True
        
    except Exception as e:
        print(f"✗ 教师模型测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("测试新的网格安全性评估方法")
    print("=" * 50)
    
    # 运行测试
    test1_passed = test_grid_safety()
    test2_passed = test_teacher_model()
    
    print("\n" + "=" * 50)
    print("测试结果总结:")
    print(f"网格安全性评估: {'✓ 通过' if test1_passed else '✗ 失败'}")
    print(f"教师模型测试: {'✓ 通过' if test2_passed else '✗ 失败'}")
    
    if test1_passed and test2_passed:
        print("🎉 所有测试通过！")
    else:
        print("❌ 部分测试失败，请检查代码")
    print("=" * 50)
