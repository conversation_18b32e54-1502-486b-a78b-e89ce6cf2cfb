"""
无人机环境封装，提供与AirSim交互的接口
"""
import time
import math
import random
import numpy as np
from typing import List, Tuple, Dict, Any

import airsim
from airsim.types import Vector3r, Quaternionr

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config import *
from environment.depth_utils import (
    process_depth_image, depth_to_point_cloud,
    create_3d_grid_from_points
)

class DroneEnvironment:
    def __init__(self, ip: str = AIRSIM_IP, port: int = AIRSIM_PORT, drone_name: str = DRONE_NAME,
                 enable_collision_detection: bool = True, min_obstacle_distance: float = MIN_OBSTACLE_DISTANCE):
        """
        初始化无人机环境

        Args:
            ip: AirSim服务器IP
            port: AirSim服务器端口
            drone_name: 无人机名称
        """
        self.drone_name = drone_name
        self.client = None
        self.connect(ip, port)

        # 相机配置
        self.camera_name = CAMERA_NAME
        self.camera_fov_h = CAMERA_FOV_H
        self.depth_width = DEPTH_WIDTH
        self.depth_height = DEPTH_HEIGHT
        self.depth_scale = DEPTH_SCALE
        self.max_depth = MAX_DEPTH
        self.sky_depth = SKY_DEPTH

        # 3D网格配置
        self.grid_horizontal_bins = GRID_HORIZONTAL_BINS
        self.grid_vertical_bins = GRID_VERTICAL_BINS
        self.grid_depth_bins = GRID_DEPTH_BINS
        self.max_detection_range = MAX_DETECTION_RANGE

        # 无人机状态
        self.current_yaw = 0.0
        self.is_flying = False

        # 转向控制相关参数
        self.target_yaw = 0.0  # 目标偏航角
        self.max_yaw_rate = 30.0  # 最大偏航角速度（度/秒）
        self.yaw_rate = 0.0  # 当前偏航角速度
        self.smooth_factor = 0.3  # 平滑因子，值越小转向越平滑

        # 碰撞检测设置
        self.enable_collision_detection = enable_collision_detection
        self.min_obstacle_distance = min_obstacle_distance  # 最小障碍物距离

        # 缓存最近的深度图像和点云，用于可视化和调试
        self.last_depth_image = None
        self.last_point_cloud = None
        self.last_3d_grid = None

    def connect(self, ip: str, port: int) -> None:
        """
        连接到AirSim

        Args:
            ip: AirSim服务器IP
            port: AirSim服务器端口
        """
        try:
            self.client = airsim.MultirotorClient(ip=ip, port=port)
            self.client.confirmConnection()
            self.client.enableApiControl(True, vehicle_name=self.drone_name)
            print(f"Connected to AirSim at {ip}:{port}")
        except Exception as e:
            print(f"Failed to connect to AirSim: {e}")
            raise

    def reset(self) -> np.ndarray:
        """
        重置环境

        Returns:
            初始观测（雷达数据）
        """
        self.client.reset()
        self.client.enableApiControl(True, vehicle_name=self.drone_name)
        self.client.armDisarm(True, vehicle_name=self.drone_name)

        # 起飞到指定高度
        self.takeoff(TAKEOFF_HEIGHT)

        # 设置随机偏航角（-180到180度之间）
        self.current_yaw = random.uniform(-180.0, 180.0)
        self.target_yaw = self.current_yaw  # 初始目标偏航角与当前偏航角相同
        self.yaw_rate = 0.0  # 重置偏航角速度
        print(f"设置随机初始偏航角: {self.current_yaw:.2f}度")

        # 应用随机偏航角
        self.client.rotateToYawAsync(
            self.current_yaw,
            5.0,  # 旋转速度
            vehicle_name=self.drone_name
        ).join()

        # 获取初始观测
        return self.get_observation()

    def takeoff(self, height: float) -> None:
        """
        起飞到指定高度

        Args:
            height: 起飞高度（米）
        """
        print(f"Taking off to {height}m...")
        self.client.takeoffAsync(vehicle_name=self.drone_name).join()

        # 飞到指定高度
        self.client.moveToZAsync(
            -height,  # AirSim中z轴向下为正
            5.0,  # 速度
            vehicle_name=self.drone_name
        ).join()

        self.is_flying = True
        print(f"Reached target height: {height}m")

    def get_depth_image(self) -> np.ndarray:
        """
        获取深度图像

        Returns:
            处理后的深度图像
        """
        try:
            # 获取深度图像
            responses = self.client.simGetImages([
                airsim.ImageRequest(self.camera_name, airsim.ImageType.DepthPerspective, True, False)
            ], vehicle_name=self.drone_name)

            if not responses or not responses[0].width:
                print("警告: 未收到深度图像")
                # 返回一个空的深度图像
                return np.full((self.depth_height, self.depth_width), self.max_depth)

            # 将深度图像转换为numpy数组
            depth_img = airsim.list_to_2d_float_array(responses[0].image_data_float,
                                                     responses[0].width,
                                                     responses[0].height)

            # 处理深度图像（处理无效值和天空）
            processed_depth = process_depth_image(depth_img, self.max_depth, self.sky_depth)

            # 缓存深度图像
            self.last_depth_image = processed_depth

            return processed_depth

        except Exception as e:
            print(f"获取深度图像时出错: {e}")
            # 返回一个空的深度图像
            return np.full((self.depth_height, self.depth_width), self.max_depth)

    def depth_to_3d_grid(self, depth_img: np.ndarray) -> np.ndarray:
        """
        将深度图像转换为3D网格

        Args:
            depth_img: 深度图像

        Returns:
            3D网格数组
        """
        # 将深度图像转换为点云
        points = depth_to_point_cloud(depth_img, self.camera_fov_h, self.depth_scale)

        # 缓存点云
        self.last_point_cloud = points

        # 如果没有点，返回空的3D网格
        if len(points) == 0:
            empty_grid = np.full((self.grid_horizontal_bins, self.grid_vertical_bins, self.grid_depth_bins),
                               self.max_detection_range)
            self.last_3d_grid = empty_grid
            return empty_grid

        # 创建3D网格
        grid_3d = create_3d_grid_from_points(
            points,
            self.grid_horizontal_bins,
            self.grid_vertical_bins,
            self.grid_depth_bins,
            self.max_detection_range
        )

        # 缓存3D网格
        self.last_3d_grid = grid_3d

        return grid_3d



    def get_observation(self) -> np.ndarray:
        """
        获取当前观测

        Returns:
            深度图像
        """
        # 获取深度图像
        depth_img = self.get_depth_image()

        # 创建3D网格（用于缓存）
        self.depth_to_3d_grid(depth_img)

        # 返回深度图像作为观测
        return depth_img

    def get_current_height(self) -> float:
        """
        获取当前高度

        Returns:
            当前高度（米）
        """
        try:
            drone_state = self.client.getMultirotorState(vehicle_name=self.drone_name)
            current_height = -drone_state.kinematics_estimated.position.z_val  # 注意AirSim中z轴向下为正
            return current_height
        except Exception as e:
            print(f"获取高度时出错: {e}")
            return 0.0

    def step(self, action) -> Tuple[np.ndarray, float, bool, Dict[str, Any]]:
        """
        执行一步动作

        Args:
            action: 三维速度向量 [Vx, Vy, Vz] 或转向角（向后兼容）

        Returns:
            (observation, reward, done, info)
        """
        # 检查action类型，支持向后兼容
        if isinstance(action, (int, float)):
            # 旧格式：转向角
            print("[兼容模式] 检测到转向角输入，转换为三维速度向量")
            turn_angle_rad = np.deg2rad(action)
            vx = FORWARD_SPEED * np.cos(turn_angle_rad)
            vy = FORWARD_SPEED * np.sin(turn_angle_rad)
            vz = 0.0
            velocity_vector = np.array([vx, vy, vz])
        elif isinstance(action, (list, np.ndarray)) and len(action) == 3:
            # 新格式：三维速度向量
            velocity_vector = np.array(action)
            vx, vy, vz = velocity_vector
        else:
            raise ValueError(f"Invalid action format: {action}. Expected float (turn angle) or 3D velocity vector.")

        # 打印速度信息
        print(f"[3D控制] 速度向量: Vx={vx:.2f}, Vy={vy:.2f}, Vz={vz:.2f}")
        print(f"[3D控制] 水平速度大小: {np.linalg.norm([vx, vy]):.2f}m/s")

        # 获取当前观测
        current_observation = self.get_observation()

        # 获取3D网格（用于计算奖励）
        grid_3d = self.last_3d_grid
        if grid_3d is None:
            # 如果没有3D网格，使用默认值
            grid_3d = np.full((self.grid_horizontal_bins, self.grid_vertical_bins, self.grid_depth_bins),
                            self.max_detection_range)

        # 导入math模块
        import math
        from config import ROTATION_THRESHOLD, MAX_TURN_ANGLE

        # 计算目标偏航角（基于水平速度方向）
        should_rotate = False
        if abs(vx) > 0.01 or abs(vy) > 0.01:  # 如果有明显的水平移动
            target_yaw_rad = math.atan2(vy, vx)  # 计算目标偏航角（弧度）
            target_yaw_deg = math.degrees(target_yaw_rad)

            # 获取当前偏航角
            try:
                drone_state = self.client.getMultirotorState(vehicle_name=self.drone_name)
                orientation = drone_state.kinematics_estimated.orientation
                current_yaw_rad = math.atan2(2.0 * (orientation.w_val * orientation.z_val + orientation.x_val * orientation.y_val),
                                           1.0 - 2.0 * (orientation.y_val * orientation.y_val + orientation.z_val * orientation.z_val))
                current_yaw_deg = math.degrees(current_yaw_rad)

                # 计算角度差
                yaw_diff = target_yaw_deg - current_yaw_deg
                # 处理角度跨越±180度的情况
                if yaw_diff > 180:
                    yaw_diff -= 360
                elif yaw_diff < -180:
                    yaw_diff += 360

                # 限制最大转向角度
                if abs(yaw_diff) > MAX_TURN_ANGLE:
                    yaw_diff = MAX_TURN_ANGLE if yaw_diff > 0 else -MAX_TURN_ANGLE
                    target_yaw_deg = current_yaw_deg + yaw_diff

                print(f"[3D控制] 当前偏航角: {current_yaw_deg:.2f}度, 目标偏航角: {target_yaw_deg:.2f}度, 角度差: {yaw_diff:.2f}度")

                # 只有当角度差大于阈值时才旋转
                if abs(yaw_diff) > ROTATION_THRESHOLD * 180 / math.pi:  # 转换为度
                    should_rotate = True
                    print(f"[3D控制] 需要旋转 {yaw_diff:.2f}度")

                    # 调整朝向
                    self.client.rotateToYawAsync(
                        target_yaw_deg,
                        10.0,  # 提高旋转速度（度/秒）
                        vehicle_name=self.drone_name
                    )

                    # 等待一小段时间让旋转开始
                    time.sleep(0.05)  # 减少等待时间
                else:
                    print(f"[3D控制] 角度差较小，不需要旋转")

            except Exception as e:
                print(f"获取当前偏航角时出错: {e}")
                # 如果获取失败，仍然执行旋转
                should_rotate = True

        # 使用世界坐标系移动，不使用ForwardOnly模式
        self.client.moveByVelocityAsync(
            vx, vy, vz,  # 世界坐标系速度向量
            1.5,  # 持续时间（减少到1.5秒以便更快响应）
            vehicle_name=self.drone_name
        )

        # 获取实际的偏航角
        try:
            drone_state = self.client.getMultirotorState(vehicle_name=self.drone_name)
            orientation = drone_state.kinematics_estimated.orientation
            # 将四元数转换为欧拉角
            yaw = math.atan2(2.0 * (orientation.w_val * orientation.z_val + orientation.x_val * orientation.y_val),
                           1.0 - 2.0 * (orientation.y_val * orientation.y_val + orientation.z_val * orientation.z_val))
            self.current_yaw = math.degrees(yaw)
            print(f"[3D控制] 当前偏航角: {self.current_yaw:.2f}度")
        except Exception as e:
            print(f"获取偏航角时出错: {e}")

        # 检查是否碰撞
        collision_info = self.client.simGetCollisionInfo(vehicle_name=self.drone_name)

        # 打印碰撞信息以便调试
        if collision_info.has_collided:
            print("检测到碰撞:")
            print(f"  碰撞对象名称: {collision_info.object_name}")
            print(f"  碰撞对象ID: {collision_info.object_id}")
            print(f"  碰撞点: {collision_info.impact_point}")
            print(f"  碰撞法线: {collision_info.normal}")
            print(f"  穿透深度: {collision_info.penetration_depth}")
            print(f"  碰撞位置: {collision_info.position}")
            print(f"  时间戳: {collision_info.time_stamp}")

        # 获取当前高度
        drone_state = self.client.getMultirotorState(vehicle_name=self.drone_name)
        current_height = -drone_state.kinematics_estimated.position.z_val  # 注意AirSim中z轴向下为正
        print(f"当前高度: {current_height:.2f}m")

        # 检查是否接近地面
        if current_height < 0.5:  # 如果高度小于0.5米，认为可能是地面碰撞
            print("警告: 无人机高度过低，可能接近地面")

            # 如果高度过低，尝试向上飞行以恢复高度
            print("尝试恢复高度...")
            self.client.moveToZAsync(
                -TAKEOFF_HEIGHT,  # 恢复到设定的起飞高度
                2.0,  # 速度
                vehicle_name=self.drone_name
            ).join()

            # 重新获取高度
            drone_state = self.client.getMultirotorState(vehicle_name=self.drone_name)
            current_height = -drone_state.kinematics_estimated.position.z_val
            print(f"恢复后高度: {current_height:.2f}m")

        # 获取无人机位置
        drone_position = drone_state.kinematics_estimated.position

        # 判断是否是与地面的碰撞
        is_ground_collision = False
        ignore_collision = False

        if collision_info.has_collided:
            # 计算碰撞点与无人机的距离
            if collision_info.impact_point:
                collision_distance = math.sqrt(
                    (collision_info.impact_point.x_val - drone_position.x_val) ** 2 +
                    (collision_info.impact_point.y_val - drone_position.y_val) ** 2 +
                    (collision_info.impact_point.z_val - drone_position.z_val) ** 2
                )
                print(f"碰撞点与无人机的距离: {collision_distance:.2f}m")

                # 如果碰撞点距离无人机太远，可能是误报
                if collision_distance > 1.0:  # 如果距离大于1米
                    ignore_collision = True
                    print(f"碰撞点距离无人机较远({collision_distance:.2f}m)，可能是误报，将被忽略")

            # 检查碰撞对象名称是否包含"ground"或"terrain"
            if collision_info.object_name and ("ground" in collision_info.object_name.lower() or "terrain" in collision_info.object_name.lower()):
                is_ground_collision = True
                print("检测到与地面的碰撞，将被忽略")

            # 检查碰撞法线是否指向上方（地面碰撞的特征）
            if collision_info.normal and collision_info.normal.z_val > 0.8:  # 法线z分量大于0.8，表示大致指向上方
                is_ground_collision = True
                print("根据碰撞法线判断为地面碰撞，将被忽略")

            # 检查碰撞高度是否接近地面
            if current_height < 0.3:
                is_ground_collision = True
                print("根据高度判断为地面碰撞，将被忽略")

            # 打印无人机当前位置
            print(f"无人机位置: x={drone_position.x_val:.2f}, y={drone_position.y_val:.2f}, z={drone_position.z_val:.2f}")

            # 如果碰撞对象是建筑物，获取更多信息
            if "building" in collision_info.object_name.lower():
                # 尝试获取障碍物的位置和尺寸
                try:
                    # 获取场景中的所有对象
                    objects = self.client.simListSceneObjects()
                    print(f"场景中的对象数量: {len(objects)}")

                    # 查找碰撞的对象
                    for obj_name in objects:
                        if collision_info.object_name in obj_name:
                            print(f"找到碰撞对象: {obj_name}")
                            # 获取对象的姿态
                            obj_pose = self.client.simGetObjectPose(obj_name)
                            print(f"对象位置: x={obj_pose.position.x_val:.2f}, y={obj_pose.position.y_val:.2f}, z={obj_pose.position.z_val:.2f}")

                            # 计算无人机到对象的距离
                            obj_distance = math.sqrt(
                                (obj_pose.position.x_val - drone_position.x_val) ** 2 +
                                (obj_pose.position.y_val - drone_position.y_val) ** 2 +
                                (obj_pose.position.z_val - drone_position.z_val) ** 2
                            )
                            print(f"无人机到对象的距离: {obj_distance:.2f}m")
                            break
                except Exception as e:
                    print(f"获取对象信息时出错: {e}")

        # 根据碰撞检测设置决定是否检测碰撞
        if self.enable_collision_detection:
            # 只有非地面碰撞且不忽略的碰撞才结束回合
            done = collision_info.has_collided and not (is_ground_collision or ignore_collision)
        else:
            # 如果禁用碰撞检测，则永远不会因碰撞而结束回合
            done = False
            if collision_info.has_collided:
                print("检测到碰撞，但由于碰撞检测已禁用，将被忽略")

        # 计算最小距离
        min_dist = np.min(grid_3d)

        # 计算奖励
        reward = self._compute_reward(grid_3d, velocity_vector, done)

        # 额外信息
        info = {
            "collision": done,
            "position": self.client.getMultirotorState(vehicle_name=self.drone_name).kinematics_estimated.position,
            "yaw": self.current_yaw,
            "velocity_vector": velocity_vector,
            "min_distance": min_dist,
            "grid_3d": grid_3d,
            "depth_image": self.last_depth_image,
            "current_height": self.get_current_height()
        }

        return current_observation, reward, done, info

    def _compute_reward(self, grid_3d: np.ndarray, velocity_vector: np.ndarray, collision: bool) -> float:
        """
        计算奖励

        Args:
            grid_3d: 3D网格数组
            velocity_vector: 三维速度向量
            collision: 是否碰撞

        Returns:
            奖励值
        """
        if collision:
            return -100.0  # 碰撞惩罚

        # 基础奖励：前进
        reward = 1.0

        # 奖励前向速度，惩罚过大的侧向速度
        vx, vy, vz = velocity_vector
        reward += 0.1 * vx  # 奖励前向速度
        reward -= 0.05 * abs(vy)  # 惩罚侧向速度
        reward -= 0.02 * abs(vz)  # 惩罚垂直速度

        # 奖励与障碍物的距离
        min_distance = np.min(grid_3d)
        if min_distance < self.min_obstacle_distance:
            reward -= (self.min_obstacle_distance - min_distance) * 2.0

        return reward

    def close(self) -> None:
        """
        关闭环境
        """
        if self.client:
            self.client.armDisarm(False, vehicle_name=self.drone_name)
            self.client.enableApiControl(False, vehicle_name=self.drone_name)
            print("Disconnected from AirSim")
