#!/usr/bin/env python3
"""
测试3D避障系统
"""
import numpy as np
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models.teacher_model import TeacherModel
from environment.depth_utils import create_3d_grid_from_points, generate_3d_velocity_candidates, evaluate_3d_velocity_safety
from config import *

def create_test_scenarios():
    """创建测试场景"""
    scenarios = []
    
    # 场景1：前方有障碍物
    print("创建场景1：前方有障碍物")
    points1 = []
    # 在前方5米处放置障碍物
    for i in range(100):
        x = 5.0 + np.random.normal(0, 0.5)
        y = np.random.normal(0, 2.0)
        z = np.random.normal(0, 1.0)
        points1.append([x, y, z])
    scenarios.append(("前方障碍物", np.array(points1), 3.0))
    
    # 场景2：左侧有障碍物
    print("创建场景2：左侧有障碍物")
    points2 = []
    for i in range(100):
        x = np.random.uniform(2.0, 8.0)
        y = -3.0 + np.random.normal(0, 0.5)  # 左侧
        z = np.random.normal(0, 1.0)
        points2.append([x, y, z])
    scenarios.append(("左侧障碍物", np.array(points2), 3.0))
    
    # 场景3：上方有障碍物（需要下降）
    print("创建场景3：上方有障碍物")
    points3 = []
    for i in range(100):
        x = np.random.uniform(3.0, 10.0)
        y = np.random.normal(0, 2.0)
        z = -2.0 + np.random.normal(0, 0.3)  # 上方
        points3.append([x, y, z])
    scenarios.append(("上方障碍物", np.array(points3), 3.0))
    
    # 场景4：下方有障碍物（需要上升）
    print("创建场景4：下方有障碍物")
    points4 = []
    for i in range(100):
        x = np.random.uniform(3.0, 10.0)
        y = np.random.normal(0, 2.0)
        z = 2.0 + np.random.normal(0, 0.3)  # 下方
        points4.append([x, y, z])
    scenarios.append(("下方障碍物", np.array(points4), 3.0))
    
    # 场景5：高度过高（触发高度限制）
    print("创建场景5：高度过高")
    points5 = []
    for i in range(50):
        x = np.random.uniform(5.0, 15.0)
        y = np.random.normal(0, 3.0)
        z = np.random.normal(0, 2.0)
        points5.append([x, y, z])
    scenarios.append(("高度过高", np.array(points5), 6.0))  # 超过警戒高度
    
    return scenarios

def test_3d_grid_creation():
    """测试3D网格创建"""
    print("\n=== 测试3D网格创建 ===")
    
    # 创建测试点云
    points = np.array([
        [5.0, 0.0, 0.0],   # 正前方
        [3.0, -2.0, 0.0], # 左前方
        [4.0, 2.0, 0.0],  # 右前方
        [6.0, 0.0, -1.0], # 前上方
        [7.0, 0.0, 1.0],  # 前下方
    ])
    
    print(f"输入点云: {points.shape}")
    
    # 创建3D网格
    grid_3d = create_3d_grid_from_points(
        points, 
        GRID_HORIZONTAL_BINS, 
        GRID_VERTICAL_BINS, 
        GRID_DEPTH_BINS, 
        MAX_DETECTION_RANGE
    )
    
    print(f"3D网格形状: {grid_3d.shape}")
    print(f"网格最小值: {np.min(grid_3d):.2f}")
    print(f"网格最大值: {np.max(grid_3d):.2f}")
    
    # 统计非最大值的网格数量
    non_max_count = np.sum(grid_3d < MAX_DETECTION_RANGE)
    print(f"包含障碍物的网格数量: {non_max_count}")
    
    return grid_3d

def test_velocity_candidates():
    """测试速度候选生成"""
    print("\n=== 测试速度候选生成 ===")
    
    candidates = generate_3d_velocity_candidates(
        base_speed=FORWARD_SPEED, 
        num_candidates=20
    )
    
    print(f"生成候选数量: {len(candidates)}")
    print(f"候选形状: {candidates.shape}")
    
    # 分析候选分布
    vx_range = [np.min(candidates[:, 0]), np.max(candidates[:, 0])]
    vy_range = [np.min(candidates[:, 1]), np.max(candidates[:, 1])]
    vz_range = [np.min(candidates[:, 2]), np.max(candidates[:, 2])]
    
    print(f"Vx范围: [{vx_range[0]:.2f}, {vx_range[1]:.2f}]")
    print(f"Vy范围: [{vy_range[0]:.2f}, {vy_range[1]:.2f}]")
    print(f"Vz范围: [{vz_range[0]:.2f}, {vz_range[1]:.2f}]")
    
    # 显示前5个候选
    print("前5个候选:")
    for i in range(min(5, len(candidates))):
        vx, vy, vz = candidates[i]
        speed = np.linalg.norm(candidates[i])
        print(f"  {i+1}: Vx={vx:.2f}, Vy={vy:.2f}, Vz={vz:.2f}, 总速度={speed:.2f}")
    
    return candidates

def test_safety_evaluation():
    """测试安全性评估"""
    print("\n=== 测试安全性评估 ===")
    
    # 创建简单的3D网格
    grid_3d = np.full((GRID_HORIZONTAL_BINS, GRID_VERTICAL_BINS, GRID_DEPTH_BINS), MAX_DETECTION_RANGE)
    
    # 在前方中央放置障碍物
    center_h = GRID_HORIZONTAL_BINS // 2
    center_v = GRID_VERTICAL_BINS // 2
    front_d = 2  # 前方第3个深度层
    grid_3d[center_h-1:center_h+2, center_v-1:center_v+2, front_d] = 3.0  # 3米距离的障碍物
    
    print(f"测试网格形状: {grid_3d.shape}")
    print(f"障碍物位置: 水平[{center_h-1}:{center_h+2}], 垂直[{center_v-1}:{center_v+2}], 深度[{front_d}]")
    
    # 生成测试候选
    test_candidates = np.array([
        [1.0, 0.0, 0.0],   # 直行
        [0.8, 0.6, 0.0],   # 右转
        [0.8, -0.6, 0.0],  # 左转
        [0.7, 0.0, 0.3],   # 上升
        [0.7, 0.0, -0.3],  # 下降
    ])
    
    print(f"测试候选数量: {len(test_candidates)}")
    
    # 评估安全性
    safety_scores = evaluate_3d_velocity_safety(
        grid_3d, 
        test_candidates,
        drone_radius=DRONE_RADIUS,
        min_distance=MIN_OBSTACLE_DISTANCE,
        max_range=MAX_DETECTION_RANGE,
        time_horizon=2.0
    )
    
    print("安全性评估结果:")
    for i, (candidate, score) in enumerate(zip(test_candidates, safety_scores)):
        vx, vy, vz = candidate
        print(f"  候选{i+1}: Vx={vx:.2f}, Vy={vy:.2f}, Vz={vz:.2f} -> 安全评分: {score:.3f}")
    
    best_idx = np.argmax(safety_scores)
    print(f"最安全的候选: {best_idx+1} (评分: {safety_scores[best_idx]:.3f})")
    
    return safety_scores

def test_teacher_model_scenarios():
    """测试教师模型在不同场景下的表现"""
    print("\n=== 测试教师模型场景 ===")
    
    teacher = TeacherModel()
    scenarios = create_test_scenarios()
    
    for scenario_name, points, height in scenarios:
        print(f"\n--- 场景: {scenario_name} ---")
        print(f"点云数量: {len(points)}")
        print(f"当前高度: {height:.1f}m")
        
        # 创建模拟深度图像
        depth_image = np.random.rand(DEPTH_HEIGHT, DEPTH_WIDTH) * 10 + 5
        
        try:
            # 测试3D避障
            velocity_vector = teacher.get_velocity_vector(depth_image, height)
            
            vx, vy, vz = velocity_vector
            total_speed = np.linalg.norm(velocity_vector)
            horizontal_speed = np.linalg.norm(velocity_vector[:2])
            
            print(f"输出速度向量: Vx={vx:.2f}, Vy={vy:.2f}, Vz={vz:.2f}")
            print(f"总速度: {total_speed:.2f}m/s")
            print(f"水平速度: {horizontal_speed:.2f}m/s")
            
            # 分析运动策略
            if abs(vy) > 0.3:
                direction = "右转" if vy > 0 else "左转"
                print(f"策略: {direction} (侧向速度: {vy:.2f})")
            elif abs(vz) > 0.2:
                direction = "上升" if vz < 0 else "下降"  # 注意z轴方向
                print(f"策略: {direction} (垂直速度: {vz:.2f})")
            else:
                print("策略: 直行")
                
            print("✓ 场景测试成功")
            
        except Exception as e:
            print(f"✗ 场景测试失败: {e}")

def main():
    """主测试函数"""
    print("🚁 3D避障系统测试")
    print("="*50)
    
    tests = [
        ("3D网格创建", test_3d_grid_creation),
        ("速度候选生成", test_velocity_candidates),
        ("安全性评估", test_safety_evaluation),
        ("教师模型场景", test_teacher_model_scenarios),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        print(f"测试: {test_name}")
        print('='*60)
        
        try:
            test_func()
            passed += 1
            print(f"✓ {test_name} 测试通过")
        except Exception as e:
            print(f"✗ {test_name} 测试失败: {e}")
            import traceback
            traceback.print_exc()
    
    print(f"\n{'='*60}")
    print(f"测试结果: {passed}/{total} 通过")
    print('='*60)
    
    if passed == total:
        print("🎉 所有测试通过！3D避障系统准备就绪。")
        return True
    else:
        print("❌ 部分测试失败，请检查代码。")
        return False

if __name__ == "__main__":
    main()
