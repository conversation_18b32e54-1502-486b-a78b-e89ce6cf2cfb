"""
训练学生模型
"""
import os
import argparse
import numpy as np
from typing import List, Tuple

from models.student_model import StudentModel
from training.trainer import Trainer
from config import EPOCHS, BATCH_SIZE, MODEL_DIR

def parse_args():
    """
    解析命令行参数
    """
    parser = argparse.ArgumentParser(description="Train student model using collected data")
    parser.add_argument("--data_path", type=str, required=True, help="Path to training data file")
    parser.add_argument("--epochs", type=int, default=EPOCHS, help="Number of training epochs")
    parser.add_argument("--batch_size", type=int, default=BATCH_SIZE, help="Batch size")
    parser.add_argument("--model_dir", type=str, default=MODEL_DIR, help="Directory to save model")
    
    return parser.parse_args()

def main():
    """
    主函数
    """
    # 解析命令行参数
    args = parse_args()
    
    # 创建保存目录
    os.makedirs(args.model_dir, exist_ok=True)
    
    # 创建学生模型
    student = StudentModel()
    
    # 创建训练器
    trainer = Trainer(student, args.model_dir)
    
    # 加载数据
    data = np.load(args.data_path, allow_pickle=True)
    observations = data["observations"]
    actions = data["actions"]
    
    print(f"Loaded data: {len(observations)} samples")
    
    # 训练模型
    loss_history = trainer.train(observations, actions, args.epochs, args.batch_size)
    
    # 绘制损失曲线
    trainer.plot_loss(loss_history)
    
    # 评估模型
    trainer.evaluate(observations, actions)
    
    print("Training completed")

if __name__ == "__main__":
    main()
