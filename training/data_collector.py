"""
数据收集模块
"""
import numpy as np
import os
import time
from typing import List, Tuple, Dict, Any
import pickle

import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config import DATA_COLLECTION_EPISODES, DATA_COLLECTION_STEPS_PER_EPISODE, DATA_DIR
from environment.drone_env import DroneEnvironment
from models.teacher_model import TeacherModel
from utils.visualization import plot_depth_image, plot_distance_data, plot_point_cloud

class DataCollector:
    """
    数据收集器
    """
    def __init__(self, env: DroneEnvironment, teacher: TeacherModel, data_dir: str = DATA_DIR,
                 save_episodes: bool = False, visualize: bool = False, visualize_interval: int = 50):
        """
        初始化数据收集器

        Args:
            env: 无人机环境
            teacher: 教师模型
            data_dir: 数据存储目录
            save_episodes: 是否保存每个回合的数据（默认为False，只保存最终数据）
            visualize: 是否可视化（默认为False）
            visualize_interval: 可视化间隔（默认为50步）
        """
        self.env = env
        self.teacher = teacher
        self.data_dir = data_dir
        self.save_episodes = save_episodes
        self.visualize = visualize
        self.visualize_interval = visualize_interval

        # 创建数据存储目录
        os.makedirs(data_dir, exist_ok=True)

        # 创建可视化目录
        if self.visualize:
            self.vis_dir = os.path.join(data_dir, "visualizations")
            os.makedirs(self.vis_dir, exist_ok=True)

        # 数据存储
        self.depth_images = []
        self.heights = []
        self.actions = []

    def collect_episode(self, max_steps: int = DATA_COLLECTION_STEPS_PER_EPISODE, episode_num: int = 0) -> Tuple[int, float]:
        """
        收集一个回合的数据

        Args:
            max_steps: 最大步数
            episode_num: 回合编号

        Returns:
            (步数, 总奖励)
        """
        # 重置环境
        observation = self.env.reset()

        total_reward = 0.0
        steps = 0

        for step in range(max_steps):
            # 获取当前高度
            current_height = self.env.get_current_height()

            # 使用新的get_velocity_vector方法获取三维速度向量
            velocity_vector = self.teacher.get_velocity_vector(observation, current_height)

            # 存储深度图像、高度和动作
            self.depth_images.append(observation)
            self.heights.append(current_height)
            self.actions.append(velocity_vector)  # 三维速度向量

            # 可视化
            if self.visualize and step % self.visualize_interval == 0:
                # 创建回合目录
                episode_dir = os.path.join(self.vis_dir, f"episode_{episode_num}")
                os.makedirs(episode_dir, exist_ok=True)

                # 可视化深度图像
                if len(observation.shape) == 2:  # 深度图像
                    depth_img_path = os.path.join(episode_dir, f"depth_img_step_{step}.png")
                    # 对于可视化，使用速度向量的模长作为"动作"
                    action_magnitude = np.linalg.norm(velocity_vector[:2])  # 水平速度大小
                    plot_depth_image(observation, action_magnitude, depth_img_path)

                    # 可视化距离数组（如果有）
                    if self.env.last_distance_array is not None:
                        distance_array_path = os.path.join(episode_dir, f"distance_array_step_{step}.png")
                        plot_distance_data(self.env.last_distance_array, action_magnitude, distance_array_path)

                    # 可视化点云（如果有）
                    if self.env.last_point_cloud is not None:
                        point_cloud_path = os.path.join(episode_dir, f"point_cloud_step_{step}.png")
                        plot_point_cloud(self.env.last_point_cloud, point_cloud_path)
                else:  # 距离数组
                    distance_array_path = os.path.join(episode_dir, f"distance_array_step_{step}.png")
                    action_magnitude = np.linalg.norm(velocity_vector[:2])
                    plot_distance_data(observation, action_magnitude, distance_array_path)

            # 执行动作
            next_observation, reward, done, info = self.env.step(velocity_vector)

            # 更新观测和奖励
            observation = next_observation
            total_reward += reward
            steps += 1

            # 打印进度
            if step % 10 == 0:
                print(f"Step {step}/{max_steps}, Velocity: {velocity_vector}, Reward: {reward:.2f}")

            # 如果回合结束，退出循环
            if done:
                print(f"Episode ended after {step + 1} steps due to collision")
                break

        return steps, total_reward

    def collect_data(self, episodes: int = DATA_COLLECTION_EPISODES, max_steps: int = DATA_COLLECTION_STEPS_PER_EPISODE) -> Tuple[np.ndarray, np.ndarray]:
        """
        收集多个回合的数据

        Args:
            episodes: 回合数
            max_steps: 每个回合的最大步数

        Returns:
            (observations, actions)
        """
        print(f"Starting data collection: {episodes} episodes, {max_steps} steps per episode")

        # 清空数据
        self.depth_images = []
        self.heights = []
        self.actions = []

        # 用于累积所有回合的数据
        all_depth_images = []
        all_heights = []
        all_actions = []

        # 收集数据
        for episode in range(episodes):
            print(f"\nEpisode {episode + 1}/{episodes}")

            # 清空当前回合的数据
            self.depth_images = []
            self.heights = []
            self.actions = []

            steps, total_reward = self.collect_episode(max_steps, episode)
            print(f"Episode {episode + 1} completed: {steps} steps, total reward: {total_reward:.2f}")

            # 如果设置了保存每个回合的数据，则保存
            if self.save_episodes:
                self.save_data(f"episode_{episode + 1}")

            # 累积数据
            all_depth_images.extend(self.depth_images)
            all_heights.extend(self.heights)
            all_actions.extend(self.actions)

        # 更新为所有回合的累积数据
        self.depth_images = all_depth_images
        self.heights = all_heights
        self.actions = all_actions

        # 转换为numpy数组
        depth_images = np.array(self.depth_images)
        heights = np.array(self.heights)
        actions = np.array(self.actions)

        print(f"Data collection completed: {len(depth_images)} samples collected")
        print(f"Depth images shape: {depth_images.shape}")
        print(f"Heights shape: {heights.shape}")
        print(f"Actions shape: {actions.shape}")

        # 保存数据统计信息
        if len(depth_images) > 0:
            print(f"Depth image statistics:")
            print(f"  Min depth: {np.min(depth_images):.2f}")
            print(f"  Max depth: {np.max(depth_images):.2f}")
            print(f"  Mean depth: {np.mean(depth_images):.2f}")

            print(f"Height statistics:")
            print(f"  Min height: {np.min(heights):.2f}")
            print(f"  Max height: {np.max(heights):.2f}")
            print(f"  Mean height: {np.mean(heights):.2f}")

            print(f"Actions statistics (3D velocity vectors):")
            print(f"  Vx - Min: {np.min(actions[:, 0]):.2f}, Max: {np.max(actions[:, 0]):.2f}, Mean: {np.mean(actions[:, 0]):.2f}")
            print(f"  Vy - Min: {np.min(actions[:, 1]):.2f}, Max: {np.max(actions[:, 1]):.2f}, Mean: {np.mean(actions[:, 1]):.2f}")
            print(f"  Vz - Min: {np.min(actions[:, 2]):.2f}, Max: {np.max(actions[:, 2]):.2f}, Mean: {np.mean(actions[:, 2]):.2f}")

        return depth_images, heights, actions

    def save_data(self, suffix: str = "") -> None:
        """
        保存数据

        Args:
            suffix: 文件名后缀
        """
        # 生成文件名
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        filename = f"depth_data_{timestamp}"
        if suffix:
            filename += f"_{suffix}"
        filename += ".npz"

        # 保存数据
        data = {
            "depth_images": np.array(self.depth_images),
            "heights": np.array(self.heights),
            "actions": np.array(self.actions)
        }

        filepath = os.path.join(self.data_dir, filename)
        np.savez_compressed(filepath, **data)

        print(f"Data saved to {filepath}")

        # 保存数据统计信息
        depth_images = np.array(self.depth_images)
        heights = np.array(self.heights)
        actions = np.array(self.actions)

        stats_filename = f"depth_data_{timestamp}"
        if suffix:
            stats_filename += f"_{suffix}"
        stats_filename += "_stats.txt"
        stats_filepath = os.path.join(self.data_dir, stats_filename)

        with open(stats_filepath, "w") as f:
            f.write(f"Data statistics:\n")
            f.write(f"Samples: {len(depth_images)}\n")
            f.write(f"Depth images shape: {depth_images.shape}\n")
            f.write(f"Heights shape: {heights.shape}\n")
            f.write(f"Actions shape: {actions.shape}\n\n")

            if len(depth_images) > 0:
                f.write(f"Depth image statistics:\n")
                f.write(f"  Min depth: {np.min(depth_images):.2f}\n")
                f.write(f"  Max depth: {np.max(depth_images):.2f}\n")
                f.write(f"  Mean depth: {np.mean(depth_images):.2f}\n\n")

                f.write(f"Height statistics:\n")
                f.write(f"  Min height: {np.min(heights):.2f}\n")
                f.write(f"  Max height: {np.max(heights):.2f}\n")
                f.write(f"  Mean height: {np.mean(heights):.2f}\n\n")

                f.write(f"Actions statistics (3D velocity vectors):\n")
                f.write(f"  Vx - Min: {np.min(actions[:, 0]):.2f}, Max: {np.max(actions[:, 0]):.2f}, Mean: {np.mean(actions[:, 0]):.2f}\n")
                f.write(f"  Vy - Min: {np.min(actions[:, 1]):.2f}, Max: {np.max(actions[:, 1]):.2f}, Mean: {np.mean(actions[:, 1]):.2f}\n")
                f.write(f"  Vz - Min: {np.min(actions[:, 2]):.2f}, Max: {np.max(actions[:, 2]):.2f}, Mean: {np.mean(actions[:, 2]):.2f}\n")

        print(f"Data statistics saved to {stats_filepath}")

    def load_data(self, filepath: str) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """
        加载数据

        Args:
            filepath: 文件路径

        Returns:
            (depth_images, heights, actions)
        """
        # 检查文件扩展名
        if filepath.endswith('.pkl'):
            # 旧格式（pickle），需要转换
            with open(filepath, "rb") as f:
                data = pickle.load(f)
            # 旧格式只有observations和actions，需要处理
            if "observations" in data and "actions" in data:
                print("Warning: Loading old format data. Heights will be set to default value.")
                depth_images = data["observations"]
                heights = np.ones(len(depth_images)) * 1.5  # 默认高度
                # 旧格式的actions是转向角，需要转换为3D速度向量
                old_actions = data["actions"]
                actions = []
                for action in old_actions:
                    if isinstance(action, (list, np.ndarray)):
                        action = action[0] if len(action) > 0 else 0.0
                    turn_angle_rad = np.deg2rad(action)
                    vx = 1.0 * np.cos(turn_angle_rad)  # 假设速度为1m/s
                    vy = 1.0 * np.sin(turn_angle_rad)
                    vz = 0.0
                    actions.append([vx, vy, vz])
                actions = np.array(actions)
            else:
                raise ValueError("Invalid old format data")
        else:
            # 新格式（npz）
            data = np.load(filepath)
            if "depth_images" in data and "heights" in data and "actions" in data:
                # 新格式
                depth_images = data["depth_images"]
                heights = data["heights"]
                actions = data["actions"]
            elif "observations" in data and "actions" in data:
                # 兼容旧格式
                print("Warning: Loading old format npz data. Heights will be set to default value.")
                depth_images = data["observations"]
                heights = np.ones(len(depth_images)) * 1.5
                old_actions = data["actions"]
                actions = []
                for action in old_actions:
                    if isinstance(action, (list, np.ndarray)):
                        action = action[0] if len(action) > 0 else 0.0
                    turn_angle_rad = np.deg2rad(action)
                    vx = 1.0 * np.cos(turn_angle_rad)
                    vy = 1.0 * np.sin(turn_angle_rad)
                    vz = 0.0
                    actions.append([vx, vy, vz])
                actions = np.array(actions)
            else:
                raise ValueError("Invalid data format")

        print(f"Data loaded from {filepath}: {len(depth_images)} samples")
        print(f"Depth images shape: {depth_images.shape}")
        print(f"Heights shape: {heights.shape}")
        print(f"Actions shape: {actions.shape}")

        return depth_images, heights, actions
