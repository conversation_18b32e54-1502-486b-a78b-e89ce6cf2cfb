{"SeeDocsAt": "https://github.com/Microsoft/AirSim/blob/master/docs/settings.md", "SettingsVersion": 1.2, "SimMode": "Multirotor", "ClockSpeed": 1.0, "ViewMode": "SpringArmChase", "Vehicles": {"Drone": {"VehicleType": "SimpleFlight", "DefaultVehicleState": "Armed", "X": 0, "Y": 0, "Z": 0, "Yaw": 45, "EnableCollisionPassthrogh": false, "EnableCollisions": true, "AllowAPIAlways": true, "RC": {"RemoteControlID": 0, "AllowAPIWhenDisconnected": true}, "Sensors": {"LidarSensor": {"SensorType": 6, "Enabled": true, "NumberOfChannels": 1, "RotationsPerSecond": 10, "PointsPerSecond": 5000, "X": 0, "Y": 0, "Z": 0, "Roll": 0, "Pitch": 0, "Yaw": 0, "VerticalFOVUpper": 0, "VerticalFOVLower": 0, "HorizontalFOVStart": -180, "HorizontalFOVEnd": 180, "DrawDebugPoints": true, "DrawDebugLines": true, "DataFrame": "SensorLocalFrame"}}}}}