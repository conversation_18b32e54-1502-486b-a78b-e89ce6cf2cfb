"""
测试激光雷达数据获取
"""
import airsim
import numpy as np
import time
import matplotlib.pyplot as plt

def main():
    """
    主函数
    """
    try:
        # 连接到AirSim
        client = airsim.MultirotorClient()
        client.confirmConnection()
        print("成功连接到AirSim!")
        
        # 获取无人机名称
        vehicle_name = "Drone"
        print(f"使用无人机: {vehicle_name}")
        
        # 启用API控制
        client.enableApiControl(True, vehicle_name=vehicle_name)
        client.armDisarm(True, vehicle_name=vehicle_name)
        print("API控制已启用")
        
        # 起飞
        print("起飞中...")
        client.takeoffAsync(vehicle_name=vehicle_name).join()
        
        # 飞到5米高度
        print("飞到5米高度...")
        client.moveToZAsync(-5.0, 2.0, vehicle_name=vehicle_name).join()
        
        # 获取激光雷达数据
        print("获取激光雷达数据...")
        lidar_data = client.getLidarData(lidar_name="LidarSensor", vehicle_name=vehicle_name)
        
        # 打印激光雷达信息
        print(f"时间戳: {lidar_data.time_stamp}")
        print(f"点云数量: {len(lidar_data.point_cloud) // 3}")
        
        # 如果有点云数据，绘制点云
        if len(lidar_data.point_cloud) > 3:
            # 将点云数据转换为numpy数组
            points = np.array(lidar_data.point_cloud).reshape(-1, 3)
            
            # 绘制3D点云
            fig = plt.figure(figsize=(10, 8))
            ax = fig.add_subplot(111, projection='3d')
            ax.scatter(points[:, 0], points[:, 1], points[:, 2], c='r', marker='o')
            ax.set_xlabel('X')
            ax.set_ylabel('Y')
            ax.set_zlabel('Z')
            ax.set_title('激光雷达点云')
            plt.savefig('lidar_points.png')
            print("点云图已保存为 lidar_points.png")
        else:
            print("未收到点云数据")
        
        # 降落
        print("降落中...")
        client.landAsync(vehicle_name=vehicle_name).join()
        
        # 禁用API控制
        client.armDisarm(False, vehicle_name=vehicle_name)
        client.enableApiControl(False, vehicle_name=vehicle_name)
        print("API控制已禁用")
    
    except Exception as e:
        print(f"发生错误: {e}")

if __name__ == "__main__":
    main()
