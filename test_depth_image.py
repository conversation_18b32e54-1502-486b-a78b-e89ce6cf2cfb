"""
深度图像测试脚本
用于测试深度图像的获取和处理，验证深度图像的缩放因子和有效范围
"""
import os
import sys
import time
import math
import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import airsim

def get_depth_image(client, vehicle_name="Drone1", camera_name="front_center"):
    """
    获取深度图像

    Args:
        client: AirSim客户端
        vehicle_name: 无人机名称
        camera_name: 相机名称

    Returns:
        深度图像数组
    """
    # 获取深度图像
    responses = client.simGetImages([
        airsim.ImageRequest(camera_name, airsim.ImageType.DepthPerspective, True, False)
    ], vehicle_name=vehicle_name)

    if not responses:
        print("未获取到深度图像")
        return None

    # 将深度图像转换为numpy数组
    depth_img = airsim.list_to_2d_float_array(responses[0].image_data_float, responses[0].width, responses[0].height)

    return depth_img

def visualize_depth_image(depth_img, title="深度图像", save_path=None):
    """
    可视化深度图像

    Args:
        depth_img: 深度图像数组
        title: 图像标题
        save_path: 保存路径
    """
    plt.figure(figsize=(10, 6))
    plt.imshow(depth_img, cmap='jet')  # 使用jet颜色映射
    plt.colorbar(label='深度 (m)')
    plt.title(title)

    if save_path:
        plt.savefig(save_path)
        print(f"深度图像已保存到 {save_path}")
    else:
        plt.show()

    plt.close()

def depth_to_point_cloud(depth_img, fov_h=90.0, vehicle_name="Drone1", client=None):
    """
    将深度图像转换为点云

    Args:
        depth_img: 深度图像数组
        fov_h: 水平视场角（度）
        vehicle_name: 无人机名称
        client: AirSim客户端（用于获取无人机姿态）

    Returns:
        点云数组 (N, 3)
    """
    height, width = depth_img.shape

    # 计算相机内参
    f = width / (2 * math.tan(math.radians(fov_h) / 2))  # 焦距
    cx = width / 2  # 光心x
    cy = height / 2  # 光心y

    # 创建网格
    xs, ys = np.meshgrid(np.arange(width), np.arange(height))

    # 计算归一化坐标
    x_norm = (xs - cx) / f
    y_norm = (ys - cy) / f

    # 计算点云坐标 (相机坐标系)
    # 注意：AirSim中深度值可能需要缩放
    points_x = depth_img * x_norm
    points_y = depth_img * y_norm
    points_z = depth_img

    # 创建点云数组
    points = np.stack((points_x.flatten(), points_y.flatten(), points_z.flatten()), axis=1)

    # 过滤无效点（深度为0或无穷大）
    valid_indices = np.isfinite(points[:, 2]) & (points[:, 2] > 0)
    points = points[valid_indices]

    # 如果提供了客户端，将点云转换到世界坐标系
    if client:
        # 获取无人机姿态
        drone_state = client.simGetVehiclePose(vehicle_name=vehicle_name)
        position = drone_state.position
        orientation = drone_state.orientation

        # TODO: 实现坐标转换
        # 这里需要使用四元数将点云从相机坐标系转换到世界坐标系
        # 暂时省略，直接返回相机坐标系下的点云

    return points

def visualize_point_cloud(points, title="点云", save_path=None):
    """
    可视化点云

    Args:
        points: 点云数组 (N, 3)
        title: 图像标题
        save_path: 保存路径
    """
    fig = plt.figure(figsize=(10, 8))
    ax = fig.add_subplot(111, projection='3d')

    # 绘制点云
    ax.scatter(points[:, 0], points[:, 1], points[:, 2], c=points[:, 2], cmap='jet', marker='.', s=1)

    # 设置坐标轴标签
    ax.set_xlabel('X')
    ax.set_ylabel('Y')
    ax.set_zlabel('Z')

    # 设置标题
    ax.set_title(title)

    # 添加颜色条
    cbar = plt.colorbar(ax.scatter(points[:, 0], points[:, 1], points[:, 2], c=points[:, 2], cmap='jet', marker='.', s=1))
    cbar.set_label('深度 (m)')

    if save_path:
        plt.savefig(save_path)
        print(f"点云图像已保存到 {save_path}")
    else:
        plt.show()

    plt.close()

def analyze_depth_image(depth_img):
    """
    分析深度图像

    Args:
        depth_img: 深度图像数组

    Returns:
        分析结果字典
    """
    # 过滤无效值
    valid_depths = depth_img[np.isfinite(depth_img) & (depth_img > 0)]

    if len(valid_depths) == 0:
        return {
            "min": None,
            "max": None,
            "mean": None,
            "median": None,
            "valid_percentage": 0
        }

    # 计算统计信息
    min_depth = np.min(valid_depths)
    max_depth = np.max(valid_depths)
    mean_depth = np.mean(valid_depths)
    median_depth = np.median(valid_depths)
    valid_percentage = len(valid_depths) / depth_img.size * 100

    return {
        "min": min_depth,
        "max": max_depth,
        "mean": mean_depth,
        "median": median_depth,
        "valid_percentage": valid_percentage
    }

def get_distance_sensor_data(client, vehicle_name="Drone1"):
    """
    获取距离传感器数据

    Args:
        client: AirSim客户端
        vehicle_name: 无人机名称

    Returns:
        距离测量值（米）
    """
    # 获取距离传感器数据
    distance_data = client.getDistanceSensorData(vehicle_name=vehicle_name)
    return distance_data


def main():
    """
    主函数
    """
    # 创建输出目录
    output_dir = "depth_test_output"
    os.makedirs(output_dir, exist_ok=True)

    try:
        # 连接到AirSim
        client = airsim.MultirotorClient()
        client.confirmConnection()
        print("成功连接到AirSim!")

        # 获取无人机名称
        vehicle_name = "Drone1"  # 根据settings_rgb_deep.json中的配置
        print(f"使用无人机: {vehicle_name}")

        # 启用API控制
        client.enableApiControl(True, vehicle_name=vehicle_name)
        client.armDisarm(True, vehicle_name=vehicle_name)
        print("API控制已启用")

        # 起飞
        print("起飞中...")
        client.takeoffAsync(vehicle_name=vehicle_name).join()

        # 飞到5米高度
        print("飞到5米高度...")
        client.moveToZAsync(-5.0, 2.0, vehicle_name=vehicle_name).join()

        # 获取距离传感器数据
        print("获取距离传感器数据...")
        distance_data = get_distance_sensor_data(client, vehicle_name=vehicle_name)
        print("\n距离传感器测量值:")
        print(f"距离: {distance_data.distance:.2f}米")
        #print(f"时间戳: {distance_data.time_stamp}")
        # DistanceSensorData只包含distance和time_stamp属性

        # 获取深度图像
        print("\n获取深度图像...")
        depth_img = get_depth_image(client, vehicle_name=vehicle_name)

        if depth_img is not None:
            # 分析深度图像
            stats = analyze_depth_image(depth_img)
            print("\n深度图像统计信息:")
            print(f"最小深度: {stats['min']:.2f}米")
            print(f"最大深度: {stats['max']:.2f}米")
            print(f"平均深度: {stats['mean']:.2f}米")
            print(f"中位深度: {stats['median']:.2f}米")
            print(f"有效像素百分比: {stats['valid_percentage']:.2f}%")

            # 比较距离传感器与深度图像
            print("\n距离传感器与深度图像比较:")
            print(f"距离传感器测量值: {distance_data.distance:.2f}米")
            print(f"深度图像中位值: {stats['median']:.2f}米")
            print(f"深度图像平均值: {stats['mean']:.2f}米")
            diff_median = abs(distance_data.distance - stats['median'])
            diff_mean = abs(distance_data.distance - stats['mean'])
            print(f"与中位值差异: {diff_median:.2f}米 ({(diff_median/distance_data.distance*100):.2f}%)")
            print(f"与平均值差异: {diff_mean:.2f}米 ({(diff_mean/distance_data.distance*100):.2f}%)")

            # 检查是否存在100倍缩放
            if stats['max'] > 0:
                print("\n深度缩放检查:")
                if stats['max'] > 1000:  # 如果最大深度超过1000米，可能存在缩放
                    print("警告: 深度值可能已经被缩放（值过大）")
                elif stats['max'] < 0.1:  # 如果最大深度小于0.1米，可能存在缩放
                    print("警告: 深度值可能已经被缩放（值过小）")
                else:
                    print("深度值范围合理，可能不存在缩放")

            # 可视化深度图像
            visualize_depth_image(depth_img, title="原始深度图像", save_path=os.path.join(output_dir, "depth_image.png"))

            # 将深度图像转换为点云
            points = depth_to_point_cloud(depth_img, client=client, vehicle_name=vehicle_name)

            # 可视化点云
            visualize_point_cloud(points, title="点云", save_path=os.path.join(output_dir, "point_cloud.png"))

            # 测试深度值处理（将超过100米的值设为100米）
            processed_depth = depth_img.copy()
            processed_depth[processed_depth > 100] = 100.0

            # 可视化处理后的深度图像
            visualize_depth_image(processed_depth, title="处理后的深度图像", save_path=os.path.join(output_dir, "processed_depth.png"))

            # 将处理后的深度图像转换为点云
            processed_points = depth_to_point_cloud(processed_depth, client=client, vehicle_name=vehicle_name)

            # 可视化处理后的点云
            visualize_point_cloud(processed_points, title="处理后的点云", save_path=os.path.join(output_dir, "processed_point_cloud.png"))

            # 保存原始深度数据
            np.save(os.path.join(output_dir, "depth_data.npy"), depth_img)
            print(f"深度数据已保存到 {os.path.join(output_dir, 'depth_data.npy')}")

        # 降落
        print("降落中...")
        client.landAsync(vehicle_name=vehicle_name).join()

        # 禁用API控制
        client.armDisarm(False, vehicle_name=vehicle_name)
        client.enableApiControl(False, vehicle_name=vehicle_name)
        print("API控制已禁用")

    except Exception as e:
        print(f"发生错误: {e}")

if __name__ == "__main__":
    main()
