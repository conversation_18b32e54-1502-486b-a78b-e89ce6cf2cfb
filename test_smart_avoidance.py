#!/usr/bin/env python3
"""
测试智能避障算法的脚本
"""
import numpy as np
from environment.depth_utils import evaluate_grid_safety, find_safest_grid_direction
from config import GRID_HORIZONTAL_BINS, GRID_VERTICAL_BINS, GRID_DEPTH_BINS, MAX_DETECTION_RANGE, MIN_OBSTACLE_DISTANCE, FORWARD_SPEED

def create_test_grid_left_obstacle():
    """
    创建一个左侧有障碍物的测试网格
    """
    grid_3d = np.full((GRID_HORIZONTAL_BINS, GRID_VERTICAL_BINS, GRID_DEPTH_BINS), MAX_DETECTION_RANGE)

    # 在左侧放置障碍物（更近的距离以触发紧急模式）
    for d in range(3):  # 前3层
        for h in range(0, 8):  # 左侧更大范围
            for v in range(2, 7):  # 中央垂直区域
                grid_3d[h, v, d] = 1.0  # 设置为1.0米距离的障碍物（很危险）

    return grid_3d

def create_test_grid_right_obstacle():
    """
    创建一个右侧有障碍物的测试网格
    """
    grid_3d = np.full((GRID_HORIZONTAL_BINS, GRID_VERTICAL_BINS, GRID_DEPTH_BINS), MAX_DETECTION_RANGE)

    # 在右侧放置障碍物（更近的距离以触发紧急模式）
    for d in range(3):  # 前3层
        for h in range(8, 16):  # 右侧更大范围
            for v in range(2, 7):  # 中央垂直区域
                grid_3d[h, v, d] = 1.0  # 设置为1.0米距离的障碍物（很危险）

    return grid_3d

def create_test_grid_center_obstacle():
    """
    创建一个中央有障碍物的测试网格
    """
    grid_3d = np.full((GRID_HORIZONTAL_BINS, GRID_VERTICAL_BINS, GRID_DEPTH_BINS), MAX_DETECTION_RANGE)

    # 在中央放置障碍物
    center_h = GRID_HORIZONTAL_BINS // 2
    center_v = GRID_VERTICAL_BINS // 2

    for d in range(3):  # 前3层
        for h in range(center_h - 2, center_h + 3):  # 中央附近
            for v in range(center_v - 1, center_v + 2):  # 中央附近
                if 0 <= h < GRID_HORIZONTAL_BINS and 0 <= v < GRID_VERTICAL_BINS:
                    grid_3d[h, v, d] = 1.5  # 设置为1.5米距离的障碍物（小于EMERGENCY_DISTANCE=2.0）

    return grid_3d

def test_scenario(name, grid_3d):
    """
    测试特定场景
    """
    print(f"\n=== {name} ===")

    # 找到最安全方向
    velocity_vector = find_safest_grid_direction(
        grid_3d,
        base_speed=FORWARD_SPEED,
        min_distance=MIN_OBSTACLE_DISTANCE,
        max_range=MAX_DETECTION_RANGE
    )

    print(f"最佳速度向量: Vx={velocity_vector[0]:.3f}, Vy={velocity_vector[1]:.3f}, Vz={velocity_vector[2]:.3f}")

    # 计算方向角度
    if abs(velocity_vector[0]) > 0.001:
        angle_deg = np.degrees(np.arctan2(velocity_vector[1], velocity_vector[0]))
        print(f"水平转向角: {angle_deg:.2f}度")

        if angle_deg > 10:
            direction = "右转"
        elif angle_deg < -10:
            direction = "左转"
        else:
            direction = "直行"
        print(f"避障动作: {direction}")

    return velocity_vector

def main():
    """
    主测试函数
    """
    print("开始测试智能避障算法...")

    # 测试场景1：左侧有障碍物，应该右转
    grid_left = create_test_grid_left_obstacle()
    velocity_left = test_scenario("左侧障碍物测试", grid_left)

    # 测试场景2：右侧有障碍物，应该左转
    grid_right = create_test_grid_right_obstacle()
    velocity_right = test_scenario("右侧障碍物测试", grid_right)

    # 测试场景3：中央有障碍物，应该选择更安全的一侧
    grid_center = create_test_grid_center_obstacle()
    velocity_center = test_scenario("中央障碍物测试", grid_center)

    # 分析结果
    print(f"\n=== 结果分析 ===")

    # 左侧障碍物场景
    left_angle = np.degrees(np.arctan2(velocity_left[1], velocity_left[0]))
    print(f"左侧障碍物 -> 转向角: {left_angle:.1f}度 ({'右转' if left_angle > 0 else '左转'})")

    # 右侧障碍物场景
    right_angle = np.degrees(np.arctan2(velocity_right[1], velocity_right[0]))
    print(f"右侧障碍物 -> 转向角: {right_angle:.1f}度 ({'右转' if right_angle > 0 else '左转'})")

    # 中央障碍物场景
    center_angle = np.degrees(np.arctan2(velocity_center[1], velocity_center[0]))
    print(f"中央障碍物 -> 转向角: {center_angle:.1f}度 ({'右转' if center_angle > 0 else '左转'})")

    # 验证智能性
    print(f"\n=== 智能性验证 ===")
    if left_angle > 0 and right_angle < 0:
        print("✅ 算法能够根据障碍物位置智能选择转向方向")
    else:
        print("❌ 算法没有根据障碍物位置智能选择转向方向")

    if abs(left_angle - right_angle) > 90:
        print("✅ 算法在不同场景下产生了明显不同的避障行为")
    else:
        print("❌ 算法在不同场景下的避障行为差异不够明显")

if __name__ == "__main__":
    main()
