#!/usr/bin/env python3
"""
测试新的避障方法 - 模拟运行（无需AirSim）
"""
import numpy as np
import sys
import os
import time

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from models.teacher_model import TeacherModel
from config import FORWARD_SPEED, MIN_OBSTACLE_DISTANCE

def create_test_scenarios():
    """创建不同的测试场景"""
    scenarios = []
    
    # 场景1：前方有障碍物，右侧安全
    depth_img1 = np.ones((72, 128)) * 15.0
    depth_img1[30:50, 50:80] = 2.0  # 前方中央有障碍物
    scenarios.append(("前方障碍物，右侧安全", depth_img1, 2.0))
    
    # 场景2：左右都有障碍物，需要上升
    depth_img2 = np.ones((72, 128)) * 15.0
    depth_img2[35:45, 20:50] = 3.0   # 左侧障碍物
    depth_img2[35:45, 80:110] = 3.0  # 右侧障碍物
    depth_img2[40:50, 50:80] = 2.5   # 前方障碍物
    scenarios.append(("左右前方都有障碍物", depth_img2, 1.5))
    
    # 场景3：高度过高，需要下降
    depth_img3 = np.ones((72, 128)) * 15.0
    depth_img3[20:30, 60:70] = 4.0   # 上方有障碍物
    scenarios.append(("高度过高场景", depth_img3, 8.0))
    
    # 场景4：复杂环境
    depth_img4 = np.ones((72, 128)) * 15.0
    depth_img4[25:35, 30:50] = 3.5   # 左上障碍物
    depth_img4[45:55, 80:100] = 2.8  # 右下障碍物
    depth_img4[35:40, 55:75] = 4.2   # 中央障碍物
    scenarios.append(("复杂环境", depth_img4, 3.0))
    
    return scenarios

def test_avoidance_scenarios():
    """测试不同避障场景"""
    print("=" * 60)
    print("测试新的网格安全性评估避障方法")
    print("=" * 60)
    
    # 创建教师模型
    teacher = TeacherModel(min_distance=MIN_OBSTACLE_DISTANCE)
    print(f"✓ 创建教师模型，最小安全距离: {MIN_OBSTACLE_DISTANCE}m")
    print(f"✓ 配置的前进速度: {FORWARD_SPEED}m/s")
    print()
    
    # 获取测试场景
    scenarios = create_test_scenarios()
    
    for i, (scenario_name, depth_img, height) in enumerate(scenarios, 1):
        print(f"场景 {i}: {scenario_name}")
        print(f"  当前高度: {height:.1f}m")
        print(f"  深度图像形状: {depth_img.shape}")
        print(f"  最小深度: {np.min(depth_img):.1f}m")
        print(f"  最大深度: {np.max(depth_img):.1f}m")
        
        try:
            # 计算避障速度向量
            start_time = time.time()
            velocity_vector = teacher.get_velocity_vector(depth_img, current_height=height)
            computation_time = time.time() - start_time
            
            # 分析结果
            vx, vy, vz = velocity_vector
            speed = np.linalg.norm(velocity_vector)
            
            print(f"  ✓ 计算完成 (耗时: {computation_time*1000:.1f}ms)")
            print(f"  速度向量: Vx={vx:.3f}, Vy={vy:.3f}, Vz={vz:.3f}")
            print(f"  总速度: {speed:.3f}m/s")
            
            # 分析飞行方向
            if abs(vx) > 0.1:
                if vx > 0:
                    direction = "前进"
                else:
                    direction = "后退"
            else:
                direction = "悬停"
                
            if abs(vy) > 0.1:
                if vy > 0:
                    direction += "+右转"
                else:
                    direction += "+左转"
                    
            if abs(vz) > 0.1:
                if vz > 0:
                    direction += "+下降"
                else:
                    direction += "+上升"
                    
            print(f"  飞行策略: {direction}")
            
            # 验证速度大小是否合理
            if abs(speed - FORWARD_SPEED) < 0.01:
                print(f"  ✓ 速度大小正确")
            else:
                print(f"  ⚠ 速度大小异常: {speed:.3f} (期望: {FORWARD_SPEED})")
                
        except Exception as e:
            print(f"  ✗ 计算失败: {e}")
            import traceback
            traceback.print_exc()
            
        print("-" * 60)
        
    print("测试完成！")

def compare_with_old_method():
    """对比新旧方法的性能"""
    print("\n" + "=" * 60)
    print("性能对比分析")
    print("=" * 60)
    
    # 创建一个测试场景
    depth_img = np.ones((72, 128)) * 10.0
    depth_img[30:45, 50:80] = 3.0  # 前方障碍物
    
    teacher = TeacherModel()
    
    print("新方法特点:")
    print("  ✓ 直接评估网格安全性")
    print("  ✓ 选择最安全的网格方向")
    print("  ✓ 固定速度大小 = FORWARD_SPEED")
    print("  ✓ 无需生成大量随机候选")
    print("  ✓ 计算效率更高")
    print("  ✓ 结果更稳定和可预测")
    
    # 测试计算时间
    times = []
    for _ in range(10):
        start_time = time.time()
        velocity = teacher.get_velocity_vector(depth_img, current_height=2.0)
        end_time = time.time()
        times.append((end_time - start_time) * 1000)  # 转换为毫秒
        
    avg_time = np.mean(times)
    std_time = np.std(times)
    
    print(f"\n计算性能:")
    print(f"  平均计算时间: {avg_time:.1f} ± {std_time:.1f} ms")
    print(f"  最快: {np.min(times):.1f} ms")
    print(f"  最慢: {np.max(times):.1f} ms")

if __name__ == "__main__":
    # 运行避障场景测试
    test_avoidance_scenarios()
    
    # 运行性能对比
    compare_with_old_method()
    
    print("\n" + "=" * 60)
    print("🎉 新的网格安全性评估方法测试完成！")
    print("主要改进:")
    print("  1. 避障策略更加直接和高效")
    print("  2. 速度大小固定，飞行更稳定")
    print("  3. 无需生成随机候选，结果可预测")
    print("  4. 计算复杂度降低，性能提升")
    print("=" * 60)
