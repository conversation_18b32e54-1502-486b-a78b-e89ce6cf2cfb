#!/usr/bin/env python3
"""
快速测试新的网格安全性评估方法
"""
import numpy as np
import sys
import os
import time

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from models.teacher_model import TeacherModel
from config import FORWARD_SPEED

def quick_test():
    """快速测试"""
    print("快速测试新的网格安全性评估方法...")
    
    # 创建教师模型
    teacher = TeacherModel()
    print(f"✓ 创建教师模型")
    
    # 创建一个简单的测试深度图像
    depth_img = np.ones((72, 128)) * 10.0  # 10米距离
    depth_img[30:40, 60:70] = 3.0  # 在中央放置一个障碍物
    print(f"✓ 创建测试深度图像")
    
    # 测试5次，看看结果是否稳定
    print("\n测试结果:")
    for i in range(5):
        start_time = time.time()
        velocity = teacher.get_velocity_vector(depth_img, current_height=2.0)
        end_time = time.time()
        
        speed = np.linalg.norm(velocity)
        print(f"  测试 {i+1}: Vx={velocity[0]:.3f}, Vy={velocity[1]:.3f}, Vz={velocity[2]:.3f}, 速度={speed:.3f}, 时间={((end_time-start_time)*1000):.1f}ms")
        
        # 检查速度是否合理
        if abs(speed - FORWARD_SPEED) > 0.01:
            print(f"    ⚠ 速度异常: {speed:.3f} (期望: {FORWARD_SPEED})")
        
        if i < 4:  # 不在最后一次打印调试信息
            print("    " + "-" * 40)
    
    print("\n✓ 快速测试完成！")
    
    # 测试不同的场景
    print("\n测试不同场景:")
    
    # 场景1：左侧有障碍物
    depth_img_left = np.ones((72, 128)) * 10.0
    depth_img_left[30:40, 20:40] = 2.0
    velocity_left = teacher.get_velocity_vector(depth_img_left, current_height=2.0)
    print(f"  左侧障碍物: Vx={velocity_left[0]:.3f}, Vy={velocity_left[1]:.3f}, Vz={velocity_left[2]:.3f}")
    
    # 场景2：右侧有障碍物
    depth_img_right = np.ones((72, 128)) * 10.0
    depth_img_right[30:40, 90:110] = 2.0
    velocity_right = teacher.get_velocity_vector(depth_img_right, current_height=2.0)
    print(f"  右侧障碍物: Vx={velocity_right[0]:.3f}, Vy={velocity_right[1]:.3f}, Vz={velocity_right[2]:.3f}")
    
    # 场景3：高度过高
    depth_img_high = np.ones((72, 128)) * 10.0
    velocity_high = teacher.get_velocity_vector(depth_img_high, current_height=8.0)
    print(f"  高度过高: Vx={velocity_high[0]:.3f}, Vy={velocity_high[1]:.3f}, Vz={velocity_high[2]:.3f}")
    
    print("\n🎉 所有测试完成！")

if __name__ == "__main__":
    quick_test()
