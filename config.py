"""
配置文件，包含项目的所有配置参数
"""

# AirSim连接配置
AIRSIM_IP = "127.0.0.1"
AIRSIM_PORT = 41451

# 无人机配置
DRONE_NAME = "Drone1"  # 根据settings_rgb_deep.json中的配置
TAKEOFF_HEIGHT = 1.5 # 起飞高度（米）
FORWARD_SPEED = 1   # 前进速度（米/秒）
DRONE_RADIUS = 0.3  # 无人机半径（米）

# 相机配置
CAMERA_NAME = "front_center"  # 相机名称
CAMERA_FOV_H = 90.0  # 水平视场角（度）
CAMERA_FOV_V = 45.0  # 垂直视场角（度）
DEPTH_WIDTH = 128  # 深度图像宽度
DEPTH_HEIGHT = 72  # 深度图像高度
DEPTH_SCALE = 1.0  # 深度缩放因子（经测试，深度值不需要缩放）
MAX_DEPTH = 100.0  # 最大深度值（米），超过此值的被视为天空
SKY_DEPTH = 100.0  # 天空的深度值（米）

# 以下配置已废弃，使用3D网格安全性评估替代
# POINT_CLOUD_ANGLE_BINS = 72  # 点云角度分区数量（已删除）
# POINT_CLOUD_ANGLE_RANGE = 90  # 点云角度范围（度）（已删除）
# COLLISION_DETECTION_DIRECTIONS = 36  # 碰撞检测方向数量（已删除）

# 3D网格配置
GRID_HORIZONTAL_BINS = 16  # 水平方向网格数量
GRID_VERTICAL_BINS = 8   # 垂直方向网格数量
GRID_DEPTH_BINS = 10     # 深度方向网格数量
MAX_DETECTION_RANGE = 15.0  # 最大检测范围（米）

# 避障配置
MIN_OBSTACLE_DISTANCE = 3.0  # 最小障碍物距离（米）- 降低检测距离
EMERGENCY_DISTANCE = 2.0  # 紧急避障距离（米）- 更近的距离才进入紧急模式
MAX_TURN_ANGLE = 45.0  # 最大转向角（度）- 降低最大转向幅度
ROTATION_THRESHOLD = 0.1  # 旋转阈值 - 降低阈值，更容易触发旋转
DIRECTION_STABILITY_STEPS = 5  # 方向稳定性：保持同一方向的最小步数
DIRECTION_CHANGE_THRESHOLD = 0.3  # 方向改变阈值：安全性差异大于此值才允许改变方向

# 训练配置
DATA_COLLECTION_EPISODES = 100  # 数据收集的回合数
DATA_COLLECTION_STEPS_PER_EPISODE = 1000  # 每回合的步数
DATA_DIR = "data"  # 数据存储目录
MODEL_DIR = "saved_models"  # 模型存储目录

# 高度限制配置
HEIGHT_WARNING_THRESHOLD = 5.0  # 高度警戒阈值（米）
HEIGHT_RESTRICTION_RATIO = 0.4  # 高度限制时可用区域比例（下方40%区域）

# 学生模型配置 - CNN+FC架构
# 输入为深度图像 + 当前高度值
INPUT_CHANNELS = 1  # 输入通道数（深度图像为单通道）
INPUT_WIDTH = DEPTH_WIDTH  # 输入宽度
INPUT_HEIGHT = DEPTH_HEIGHT  # 输入高度
HEIGHT_INPUT_SIZE = 1  # 高度输入维度
CONV_FILTERS = [16, 32, 64]  # 卷积层滤波器数量
CONV_KERNEL_SIZES = [5, 3, 3]  # 卷积核大小
CONV_STRIDES = [2, 2, 2]  # 卷积步长
FC_SIZES = [256, 128, 64]  # 全连接层大小
OUTPUT_SIZE = 3  # 输出大小（三维速度向量：Vx, Vy, Vz）
LEARNING_RATE = 0.001  # 学习率
BATCH_SIZE = 32  # 批次大小
EPOCHS = 100  # 训练轮数
