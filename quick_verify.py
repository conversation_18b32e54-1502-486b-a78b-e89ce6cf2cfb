#!/usr/bin/env python3
"""
快速验证所有核心文件是否正常工作
"""
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试所有核心模块的导入"""
    print("🔍 测试模块导入...")

    try:
        import config
        print("✅ config.py - 导入成功")

        # 验证关键配置
        assert hasattr(config, 'GRID_HORIZONTAL_BINS'), "缺少3D网格配置"
        assert hasattr(config, 'HEIGHT_WARNING_THRESHOLD'), "缺少高度限制配置"
        assert config.OUTPUT_SIZE == 3, "输出维度应该是3"
        assert config.HEIGHT_RESTRICTION_RATIO == 0.4, "高度限制比例应该是0.4"
        print("✅ config.py - 配置验证通过")

    except Exception as e:
        print(f"❌ config.py - 导入失败: {e}")
        return False

    try:
        from environment.drone_env import DroneEnvironment
        print("✅ environment/drone_env.py - 导入成功")

        from environment.depth_utils import create_3d_grid_from_points, generate_3d_velocity_candidates
        print("✅ environment/depth_utils.py - 导入成功")

    except Exception as e:
        print(f"❌ environment模块 - 导入失败: {e}")
        return False

    try:
        from models.teacher_model import TeacherModel
        print("✅ models/teacher_model.py - 导入成功")

        from models.student_model import StudentModel
        print("✅ models/student_model.py - 导入成功")

    except Exception as e:
        print(f"❌ models模块 - 导入失败: {e}")
        return False

    try:
        from training.data_collector import DataCollector
        print("✅ training/data_collector.py - 导入成功")

        from training.trainer import Trainer
        print("✅ training/trainer.py - 导入成功")

    except Exception as e:
        print(f"❌ training模块 - 导入失败: {e}")
        return False

    return True

def test_file_existence():
    """测试关键文件是否存在"""
    print("\n📁 测试文件存在性...")

    required_files = [
        "config.py",
        "environment/drone_env.py",
        "environment/depth_utils.py",
        "models/teacher_model.py",
        "models/student_model.py",
        "training/data_collector.py",
        "training/trainer.py",
        "collect_data.py",
        "train_model.py",
        "run_teacher.py",
        "run_student.py",
        "test_3d_avoidance.py",
        "demo_3d_avoidance.py",
        "test_run_teacher.py",
        "FINAL_3D_SUMMARY.md",
        "README.md"
    ]

    missing_files = []
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} - 文件不存在")
            missing_files.append(file_path)

    return len(missing_files) == 0

def test_3d_functionality():
    """测试3D功能"""
    print("\n🎯 测试3D功能...")

    try:
        from models.teacher_model import TeacherModel
        import numpy as np

        teacher = TeacherModel()

        # 测试3D速度向量输出
        depth_image = np.random.rand(72, 128) * 10 + 5
        height = 3.0

        velocity_vector = teacher.get_velocity_vector(depth_image, height)

        assert len(velocity_vector) == 3, "速度向量应该是3维"
        assert isinstance(velocity_vector, np.ndarray), "应该返回numpy数组"

        print(f"✅ 3D速度向量输出: {velocity_vector}")

        # 测试高度限制
        high_height = 6.0
        velocity_high = teacher.get_velocity_vector(depth_image, high_height)
        print(f"✅ 高度限制测试: 高度{high_height}m, 速度{velocity_high}")

        return True

    except Exception as e:
        print(f"❌ 3D功能测试失败: {e}")
        return False

def test_student_model():
    """测试学生模型"""
    print("\n🤖 测试学生模型...")

    try:
        from models.student_model import StudentModel
        import numpy as np

        student = StudentModel()

        # 测试预测
        depth_image = np.random.rand(72, 128).astype(np.float32)
        height = 3.0

        velocity_vector = student.predict(depth_image, height)

        assert len(velocity_vector) == 3, "学生模型应该输出3维速度向量"
        print(f"✅ 学生模型3D输出: {velocity_vector}")

        return True

    except Exception as e:
        print(f"❌ 学生模型测试失败: {e}")
        return False

def main():
    """主验证函数"""
    print("🚁 快速验证3D避障系统")
    print("="*50)

    tests = [
        ("模块导入", test_imports),
        ("文件存在性", test_file_existence),
        ("3D功能", test_3d_functionality),
        ("学生模型", test_student_model),
    ]

    passed = 0
    total = len(tests)

    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} - 通过")
            else:
                print(f"❌ {test_name} - 失败")
        except Exception as e:
            print(f"❌ {test_name} - 出错: {e}")

    print(f"\n{'='*50}")
    print(f"验证结果: {passed}/{total} 通过")
    print('='*50)

    if passed == total:
        print("🎉 所有验证通过！系统准备就绪。")
        print("\n📝 下一步:")
        print("1. 运行 python test_3d_avoidance.py 进行完整测试")
        print("2. 运行 python demo_3d_avoidance.py 查看演示")
        print("3. 运行 python run_teacher.py 在AirSim中体验3D避障")
        return True
    else:
        print("❌ 部分验证失败，请检查系统。")
        return False

if __name__ == "__main__":
    main()
