"""
测试无人机起飞到30米高度的独立脚本
不依赖config.py中的配置，直接硬编码30米高度
"""
import airsim
import time
import math
import numpy as np

def main():
    """
    主函数 - 让无人机起飞到30米高度并悬停
    """
    # 连接到AirSim
    print("正在连接到AirSim...")
    client = airsim.MultirotorClient()
    client.confirmConnection()
    
    # 重置模拟器
    print("重置模拟器...")
    client.reset()
    
    # 启用API控制
    print("启用API控制...")
    client.enableApiControl(True)
    client.armDisarm(True)
    
    # 获取初始位置
    drone_state = client.getMultirotorState()
    initial_position = drone_state.kinematics_estimated.position
    print(f"初始位置: X={initial_position.x_val:.2f}, Y={initial_position.y_val:.2f}, Z={initial_position.z_val:.2f}")
    
    # 起飞
    print("起飞中...")
    client.takeoffAsync().join()
    
    # 飞到30米高度 (AirSim中Z轴向下为正，所以用负值)
    target_height = -600.0
    print(f"飞行到{abs(target_height)}米高度...")
    client.moveToZAsync(target_height, 5.0).join()
    
    # 获取当前位置
    drone_state = client.getMultirotorState()
    current_position = drone_state.kinematics_estimated.position
    print(f"当前位置: X={current_position.x_val:.2f}, Y={current_position.y_val:.2f}, Z={current_position.z_val:.2f}")
    print(f"当前高度: {abs(current_position.z_val):.2f}米")
    
    # 获取激光雷达数据
    print("获取激光雷达数据...")
    try:
        lidar_data = client.getLidarData(lidar_name="LidarSensor")
        if len(lidar_data.point_cloud) > 3:
            points = np.array(lidar_data.point_cloud, dtype=np.float32).reshape(-1, 3)
            print(f"激光雷达点数: {points.shape[0]}")
            
            # 计算点到无人机的距离
            distances = np.sqrt(np.sum(points**2, axis=1))
            min_distance = np.min(distances)
            max_distance = np.max(distances)
            print(f"最近点距离: {min_distance:.2f}米, 最远点距离: {max_distance:.2f}米")
        else:
            print("未收到激光雷达数据点")
    except Exception as e:
        print(f"获取激光雷达数据时出错: {e}")
    
    # 设置相机视角为俯视
    try:
        print("尝试设置相机为俯视视角...")
        # 设置相机位置和方向 (位于无人机上方10米，完全向下看)
        camera_position = airsim.Vector3r(0, 0, current_position.z_val - 10)
        # 创建一个向下看的四元数 (90度俯仰角)
        pitch = -math.pi/2  # -90度，向下看
        camera_orientation = airsim.to_quaternion(pitch, 0, 0)
        
        # 设置相机姿势
        camera_pose = airsim.Pose(camera_position, camera_orientation)
        client.simSetCameraPose("0", camera_pose)
        print("相机已设置为俯视视角")
    except Exception as e:
        print(f"设置相机视角时出错: {e}")
    
    # 悬停并等待用户输入
    print("\n无人机已飞行到30米高度并悬停")
    print("在AirSim窗口中，您可以使用以下键盘控制:")
    print("- F1: 切换不同的相机视角")
    print("- 右键拖动: 旋转相机")
    print("- 鼠标滚轮: 缩放视图")
    print("- F: 重置相机位置")
    print("\n按Enter键开始前进飞行，或按Ctrl+C退出...")
    
    try:
        input()  # 等待用户按Enter键
        
        # 以2米/秒的速度向前飞行10秒
        print("以2米/秒的速度向前飞行10秒...")
        vx = 2.0  # 前进速度
        client.moveByVelocityAsync(vx, 0, 0, 10).join()
        
        # 获取最终位置
        drone_state = client.getMultirotorState()
        final_position = drone_state.kinematics_estimated.position
        print(f"最终位置: X={final_position.x_val:.2f}, Y={final_position.y_val:.2f}, Z={final_position.z_val:.2f}")
        
        # 悬停5秒
        print("悬停5秒...")
        client.hoverAsync().join()
        time.sleep(5)
        
    except KeyboardInterrupt:
        print("用户中断...")
    
    # 降落
    print("降落中...")
    client.landAsync().join()
    
    # 解除API控制
    client.armDisarm(False)
    client.enableApiControl(False)
    
    print("测试完成")

if __name__ == "__main__":
    main()
