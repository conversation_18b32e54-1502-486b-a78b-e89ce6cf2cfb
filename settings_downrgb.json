{"SeeDocsAt": "https://github.com/Microsoft/AirSim/blob/master/docs/settings.md", "SettingsVersion": 1.2, "SimMode": "Multirotor", "ClockSpeed": 10.0, "ViewMode": "SpringArmChase", "SubWindows": [{"WindowID": 0, "ImageType": 0, "CameraName": "front_center", "Visible": true}], "Vehicles": {"Drone": {"VehicleType": "SimpleFlight", "DefaultVehicleState": "Armed", "EnableCollisionPassthrogh": false, "EnableCollisions": true, "AllowAPIAlways": true, "Cameras": {"front_center": {"CaptureSettings": [{"ImageType": 0, "Width": 224, "Height": 224, "FOV_Degrees": 90, "AutoExposureSpeed": 100, "AutoExposureBias": 0, "AutoExposureMaxBrightness": 0.64, "AutoExposureMinBrightness": 0.03, "MotionBlurAmount": 0, "TargetGamma": 1.0}], "X": 0.3, "Y": 0.0, "Z": 0.5, "Pitch": -90.0, "Roll": 0.0, "Yaw": 0.0}}, "RC": {"RemoteControlID": 0, "AllowAPIWhenDisconnected": true}, "Sensors": {"LidarSensor": {"SensorType": 6, "Enabled": true, "NumberOfChannels": 1, "RotationsPerSecond": 10, "PointsPerSecond": 5000, "X": 0, "Y": 0, "Z": 0, "Roll": 0, "Pitch": 0, "Yaw": 0, "VerticalFOVUpper": 0, "VerticalFOVLower": 0, "HorizontalFOVStart": -180, "HorizontalFOVEnd": 180, "DrawDebugPoints": true, "DrawDebugLines": true, "DataFrame": "SensorLocalFrame"}}}}}