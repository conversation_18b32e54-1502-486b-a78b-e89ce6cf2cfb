"""
深度图像处理工具
"""
import numpy as np
import math
from typing import List, Tuple, Dict, Any

def process_depth_image(depth_img: np.ndarray, max_depth: float = 100.0, sky_depth: float = 100.0) -> np.ndarray:
    """
    处理深度图像

    Args:
        depth_img: 深度图像数组
        max_depth: 最大深度值，超过此值的被视为天空
        sky_depth: 天空的深度值

    Returns:
        处理后的深度图像
    """
    # 创建深度图像的副本
    processed_depth = depth_img.copy()

    # 将无效值（0或NaN）设为最大深度
    processed_depth[np.isnan(processed_depth) | (processed_depth <= 0)] = max_depth

    # 将超过最大深度的值（天空）设为指定的天空深度值
    processed_depth[processed_depth > max_depth] = sky_depth

    return processed_depth

def depth_to_point_cloud(depth_img: np.ndarray, fov_h: float = 90.0, depth_scale: float = 1.0) -> np.ndarray:
    """
    将深度图像转换为点云

    Args:
        depth_img: 深度图像数组
        fov_h: 水平视场角（度）
        depth_scale: 深度缩放因子

    Returns:
        点云数组 (N, 3)，坐标系为：x向前，y向右，z向下
    """
    height, width = depth_img.shape

    # 计算相机内参
    f = width / (2 * math.tan(math.radians(fov_h) / 2))  # 焦距
    cx = width / 2  # 光心x
    cy = height / 2  # 光心y

    # 创建网格
    xs, ys = np.meshgrid(np.arange(width), np.arange(height))

    # 计算归一化坐标
    x_norm = (xs - cx) / f
    y_norm = (ys - cy) / f

    # 应用深度缩放
    scaled_depth = depth_img * depth_scale

    # 计算点云坐标 (相机坐标系)
    # AirSim相机坐标系：x向前，y向右，z向下
    points_x = scaled_depth  # 深度值对应x轴（向前）
    points_y = scaled_depth * x_norm  # 右方向对应y轴
    points_z = scaled_depth * y_norm  # 下方向对应z轴

    # 创建点云数组
    points = np.stack((points_x.flatten(), points_y.flatten(), points_z.flatten()), axis=1)

    # 过滤无效点（深度为0或无穷大）
    valid_indices = np.isfinite(points[:, 0]) & (points[:, 0] > 0)
    points = points[valid_indices]

    return points

def get_front_points(points: np.ndarray, angle_range: float = 180.0) -> np.ndarray:
    """
    获取前方指定角度范围内的点云

    Args:
        points: 点云数组 (N, 3)
        angle_range: 角度范围（度）

    Returns:
        前方点云
    """
    # 计算每个点的水平角度（在xOy平面上）
    # 注意：AirSim相机坐标系中，x向前，y向右
    angles = np.arctan2(points[:, 1], points[:, 0])  # 弧度，[-pi, pi]

    # 将角度转换为度
    angles_deg = np.degrees(angles)  # [-180, 180]

    # 计算角度范围
    half_range = angle_range / 2

    # 选择前方指定角度范围内的点
    front_indices = (angles_deg >= -half_range) & (angles_deg <= half_range)
    front_points = points[front_indices]

    return front_points

def create_3d_grid_from_points(points: np.ndarray, grid_h_bins: int = 16, grid_v_bins: int = 8,
                              grid_d_bins: int = 10, max_range: float = 15.0) -> np.ndarray:
    """
    将点云转换为3D网格，每个网格单元包含该区域内的最小距离

    Args:
        points: 点云数组 (N, 3)，坐标系为：x向前，y向右，z向下
        grid_h_bins: 水平方向网格数量
        grid_v_bins: 垂直方向网格数量
        grid_d_bins: 深度方向网格数量
        max_range: 最大检测范围（米）

    Returns:
        3D网格数组 (grid_h_bins, grid_v_bins, grid_d_bins)，值为该网格内的最小距离
    """
    if len(points) == 0:
        return np.full((grid_h_bins, grid_v_bins, grid_d_bins), max_range)

    # 过滤掉超出检测范围的点
    distances = np.linalg.norm(points, axis=1)
    valid_indices = distances <= max_range
    valid_points = points[valid_indices]

    if len(valid_points) == 0:
        return np.full((grid_h_bins, grid_v_bins, grid_d_bins), max_range)

    # 计算网格边界
    # 水平方向：基于y坐标（左右）
    y_min, y_max = -max_range * 0.7, max_range * 0.7  # 左右范围
    # 垂直方向：基于z坐标（上下）
    z_min, z_max = -max_range * 0.5, max_range * 0.5  # 上下范围
    # 深度方向：基于x坐标（前后）
    x_min, x_max = 0.5, max_range  # 前方范围，避免包含无人机自身

    # 初始化3D网格
    grid_3d = np.full((grid_h_bins, grid_v_bins, grid_d_bins), max_range)

    # 计算每个点的网格索引
    h_indices = np.floor((valid_points[:, 1] - y_min) / (y_max - y_min) * grid_h_bins).astype(int)
    v_indices = np.floor((valid_points[:, 2] - z_min) / (z_max - z_min) * grid_v_bins).astype(int)
    d_indices = np.floor((valid_points[:, 0] - x_min) / (x_max - x_min) * grid_d_bins).astype(int)

    # 确保索引在有效范围内
    h_indices = np.clip(h_indices, 0, grid_h_bins - 1)
    v_indices = np.clip(v_indices, 0, grid_v_bins - 1)
    d_indices = np.clip(d_indices, 0, grid_d_bins - 1)

    # 计算每个点到无人机的距离
    point_distances = np.linalg.norm(valid_points, axis=1)

    # 将点分配到网格中，保留每个网格的最小距离
    for i in range(len(valid_points)):
        h_idx, v_idx, d_idx = h_indices[i], v_indices[i], d_indices[i]
        grid_3d[h_idx, v_idx, d_idx] = min(grid_3d[h_idx, v_idx, d_idx], point_distances[i])

    return grid_3d


def evaluate_grid_safety(grid_3d: np.ndarray, min_distance: float = 6.0) -> np.ndarray:
    """
    评估每个网格的安全性，使用网格内最小距离作为该网格的安全性

    Args:
        grid_3d: 3D网格数组 (grid_h_bins, grid_v_bins, grid_d_bins)
        min_distance: 最小安全距离（米）

    Returns:
        安全性评分数组 (grid_h_bins, grid_v_bins, grid_d_bins)，值越高越安全
    """
    grid_h_bins, grid_v_bins, grid_d_bins = grid_3d.shape
    safety_grid = np.zeros_like(grid_3d)

    # 对每个网格单元计算安全性
    for h in range(grid_h_bins):
        for v in range(grid_v_bins):
            for d in range(grid_d_bins):
                # 获取该网格的最小距离
                min_dist_in_grid = grid_3d[h, v, d]

                # 计算安全性评分
                if min_dist_in_grid >= min_distance:
                    # 安全区域：距离越远，安全性越高
                    safety_grid[h, v, d] = min(1.0, min_dist_in_grid / (min_distance * 2))
                else:
                    # 危险区域：距离越近，安全性越低
                    safety_grid[h, v, d] = max(0.0, min_dist_in_grid / min_distance)

    return safety_grid


def find_safest_grid_direction(grid_3d: np.ndarray, base_speed: float = 1.0,
                              min_distance: float = 6.0, max_range: float = 15.0) -> np.ndarray:
    """
    找到最安全的网格方向并生成对应的速度向量

    Args:
        grid_3d: 3D网格数组 (grid_h_bins, grid_v_bins, grid_d_bins)
        base_speed: 基础速度大小（米/秒）
        min_distance: 最小安全距离（米）
        max_range: 最大检测范围（米）

    Returns:
        最安全方向的速度向量 [Vx, Vy, Vz]
    """
    # 评估每个网格的安全性
    safety_grid = evaluate_grid_safety(grid_3d, min_distance)

    grid_h_bins, grid_v_bins, grid_d_bins = grid_3d.shape

    # 网格边界
    y_min, y_max = -max_range * 0.7, max_range * 0.7
    z_min, z_max = -max_range * 0.5, max_range * 0.5
    x_min, x_max = 0.5, max_range

    # 网格单元大小
    h_step = (y_max - y_min) / grid_h_bins
    v_step = (z_max - z_min) / grid_v_bins
    d_step = (x_max - x_min) / grid_d_bins

    # 找到最安全的网格
    best_safety = -1.0
    best_h, best_v, best_d = 0, 0, 0

    # 首先检查是否有紧急情况（前方有近距离障碍物）
    min_distance_in_front = np.min(grid_3d[:, :, :min(3, grid_d_bins)])  # 检查前3层

    # 使用更严格的紧急条件：只有当障碍物非常近时才进入紧急模式
    from config import EMERGENCY_DISTANCE
    is_emergency = min_distance_in_front < EMERGENCY_DISTANCE  # 使用更小的紧急距离

    if is_emergency:
        print(f"[避障调试] 检测到紧急情况，前方最小距离: {min_distance_in_front:.2f}m")
        # 紧急情况：寻找侧向安全区域，但优先较小的转向角度
        search_layers = range(min(7, grid_d_bins))  # 搜索前7层，避免选择过远的网格
        safety_weight = 1.0
        forward_preference = 0.2  # 仍然保持一定的前方偏好
    else:
        # 正常情况：优先前方，几乎不转向
        search_layers = range(min(5, grid_d_bins))  # 只搜索前5层
        safety_weight = 1.0
        forward_preference = 0.8  # 强烈偏向前方

    # 在紧急情况下，分别评估左侧和右侧的安全性
    prefer_left = False
    if is_emergency:
        h_center = grid_h_bins // 2

        # 计算左侧区域的平均安全性（只看靠近中央的区域，避免极端转向）
        left_safety = 0.0
        left_count = 0
        for h in range(max(0, h_center - 4), h_center):  # 只看中央左侧4个网格
            for v in range(grid_v_bins):
                for d in range(min(3, grid_d_bins)):  # 只看前3层
                    if safety_grid[h, v, d] > 0.1:
                        left_safety += safety_grid[h, v, d]
                        left_count += 1
        left_avg_safety = left_safety / max(left_count, 1)

        # 计算右侧区域的平均安全性（只看靠近中央的区域）
        right_safety = 0.0
        right_count = 0
        for h in range(h_center, min(grid_h_bins, h_center + 4)):  # 只看中央右侧4个网格
            for v in range(grid_v_bins):
                for d in range(min(3, grid_d_bins)):  # 只看前3层
                    if safety_grid[h, v, d] > 0.1:
                        right_safety += safety_grid[h, v, d]
                        right_count += 1
        right_avg_safety = right_safety / max(right_count, 1)

        # 只有当一侧明显更安全时才选择该侧
        safety_diff = abs(left_avg_safety - right_avg_safety)
        if safety_diff > 0.2:  # 安全性差异大于0.2才选择
            prefer_left = left_avg_safety > right_avg_safety
            print(f"[避障调试] 左侧平均安全性: {left_avg_safety:.3f}, 右侧平均安全性: {right_avg_safety:.3f}")
            print(f"[避障调试] 选择方向: {'左侧' if prefer_left else '右侧'}")
        else:
            print(f"[避障调试] 左右两侧安全性相近，保持前方偏好")

    # 搜索最安全的网格，优先选择转向角度较小的网格
    for d in search_layers:
        for h in range(grid_h_bins):
            for v in range(grid_v_bins):
                safety = safety_grid[h, v, d]

                # 如果安全性太低，跳过
                if safety < 0.1:
                    continue

                # 计算方向偏好
                direction_bonus = 0.0

                # 前方偏好：深度越小越好
                direction_bonus += (grid_d_bins - d) / grid_d_bins * forward_preference

                # 中央偏好：水平和垂直都偏向中央
                h_center = grid_h_bins // 2
                v_center = grid_v_bins // 2

                # 计算距离中央的距离，用于限制转向幅度
                h_distance_from_center = abs(h - h_center)

                # 在紧急情况下，智能选择左右方向，但限制转向幅度
                if is_emergency and prefer_left is not False:  # 只有明确选择了方向才执行
                    # 根据左右侧安全性选择偏好方向，但限制在中央附近
                    if prefer_left:
                        # 偏向左侧，但不要太极端
                        if h < h_center and h_distance_from_center <= 3:  # 限制在中央左侧3个网格内
                            h_side_bonus = (h_center - h) / h_center * 1.0  # 降低侧向偏好权重
                        else:
                            h_side_bonus = 0.0
                    else:
                        # 偏向右侧，但不要太极端
                        if h > h_center and h_distance_from_center <= 3:  # 限制在中央右侧3个网格内
                            h_side_bonus = (h - h_center) / h_center * 1.0  # 降低侧向偏好权重
                        else:
                            h_side_bonus = 0.0

                    direction_bonus += h_side_bonus

                    # 垂直方向仍然偏向中央
                    v_penalty = abs(v - v_center) / max(v_center, 1)
                    direction_bonus += (1.0 - v_penalty * 0.3) * 0.2
                else:
                    # 正常情况下强烈偏向中央
                    h_penalty = abs(h - h_center) / max(h_center, 1)
                    v_penalty = abs(v - v_center) / max(v_center, 1)
                    direction_bonus += (1.0 - h_penalty * 0.5) * 0.5  # 增强中央偏好
                    direction_bonus += (1.0 - v_penalty * 0.3) * 0.3

                # 计算总分
                total_score = safety * safety_weight + direction_bonus

                if total_score > best_safety:
                    best_safety = total_score
                    best_h, best_v, best_d = h, v, d

    print(f"[避障调试] 最佳网格: [{best_h}, {best_v}, {best_d}], 总分: {best_safety:.3f}")
    print(f"[避障调试] 最佳网格安全性: {safety_grid[best_h, best_v, best_d]:.3f}")
    print(f"[避障调试] 最佳网格距离: {grid_3d[best_h, best_v, best_d]:.2f}m")

    # 计算最安全网格的中心位置
    center_y = y_min + (best_h + 0.5) * h_step
    center_z = z_min + (best_v + 0.5) * v_step
    center_x = x_min + (best_d + 0.5) * d_step

    print(f"[避障调试] 目标网格中心: ({center_x:.2f}, {center_y:.2f}, {center_z:.2f})")

    # 计算指向该网格的方向向量
    direction_vector = np.array([center_x, center_y, center_z])
    direction_norm = np.linalg.norm(direction_vector)

    if direction_norm > 0:
        # 归一化方向向量并乘以基础速度
        velocity_vector = direction_vector / direction_norm * base_speed
    else:
        # 如果方向向量为零，默认前进
        velocity_vector = np.array([base_speed, 0.0, 0.0])

    return velocity_vector
