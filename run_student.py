"""
运行学生模型（神经网络）
"""
import os
import time
import argparse
import numpy as np
from typing import List, Tuple

from environment.drone_env import DroneEnvironment
from models.student_model import StudentModel
from utils.visualization import visualize_drone_state, plot_trajectory, plot_depth_image, plot_distance_data, plot_point_cloud

def parse_args():
    """
    解析命令行参数
    """
    parser = argparse.ArgumentParser(description="Run student model for obstacle avoidance")
    parser.add_argument("--model_path", type=str, required=True, help="Path to trained model")
    parser.add_argument("--max_steps", type=int, default=1000, help="Maximum steps to run")
    parser.add_argument("--visualize", action="store_true", help="Visualize drone state")
    parser.add_argument("--save_path", type=str, default="results/student", help="Path to save results")

    return parser.parse_args()

def main():
    """
    主函数
    """
    # 解析命令行参数
    args = parse_args()

    # 创建保存目录
    os.makedirs(args.save_path, exist_ok=True)

    # 创建环境和模型
    env = DroneEnvironment()
    student = StudentModel()

    # 加载模型
    student.load(args.model_path)

    # 重置环境
    observation = env.reset()

    # 记录轨迹
    positions = []

    try:
        # 运行模型
        for step in range(args.max_steps):
            # 获取当前位置
            drone_state = env.client.getMultirotorState(vehicle_name=env.drone_name)
            position = drone_state.kinematics_estimated.position
            positions.append((position.x_val, position.y_val))

            # 使用学生模型预测动作
            action = student.predict(observation)

            # 可视化
            if args.visualize and step % 10 == 0:
                # 创建步骤目录
                step_dir = os.path.join(args.save_path, f"step_{step:04d}")
                os.makedirs(step_dir, exist_ok=True)

                # 可视化无人机状态
                visualize_path = os.path.join(step_dir, "drone_state.png")
                visualize_drone_state(
                    (position.x_val, position.y_val, position.z_val),
                    env.current_yaw,
                    observation,
                    save_path=visualize_path,
                    distance_array=env.last_distance_array
                )

                # 可视化深度图像（如果是深度图像）
                if len(observation.shape) == 2:  # 深度图像
                    depth_img_path = os.path.join(step_dir, "depth_image.png")
                    plot_depth_image(observation, action, depth_img_path)

                    # 可视化点云（如果有）
                    if env.last_point_cloud is not None and len(env.last_point_cloud) > 0:
                        point_cloud_path = os.path.join(step_dir, "point_cloud.png")
                        plot_point_cloud(env.last_point_cloud, point_cloud_path)

                # 可视化距离数组（如果有）
                if env.last_distance_array is not None:
                    distance_array_path = os.path.join(step_dir, "distance_array.png")
                    plot_distance_data(env.last_distance_array, action, distance_array_path)

            # 执行动作
            next_observation, reward, done, info = env.step(action)

            # 打印信息
            print(f"Step {step + 1}/{args.max_steps}, Action: {action:.2f}, Reward: {reward:.2f}")

            # 更新观测
            observation = next_observation

            # 如果回合结束，退出循环
            if done:
                print(f"Episode ended after {step + 1} steps due to collision")
                break

            # 等待一小段时间，以便观察
            time.sleep(0.05)

    except KeyboardInterrupt:
        print("Interrupted by user")

    finally:
        # 绘制轨迹
        trajectory_path = os.path.join(args.save_path, "trajectory.png")
        plot_trajectory(positions, save_path=trajectory_path)

        # 关闭环境
        env.close()

        print("Run completed")

if __name__ == "__main__":
    main()
