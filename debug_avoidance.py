#!/usr/bin/env python3
"""
调试避障算法的脚本
"""
import numpy as np
from environment.depth_utils import evaluate_grid_safety, find_safest_grid_direction
from config import GRID_HORIZONTAL_BINS, GRID_VERTICAL_BINS, GRID_DEPTH_BINS, MAX_DETECTION_RANGE, MIN_OBSTACLE_DISTANCE, FORWARD_SPEED

def create_test_grid_with_obstacle():
    """
    创建一个包含前方障碍物的测试网格
    """
    grid_3d = np.full((GRID_HORIZONTAL_BINS, GRID_VERTICAL_BINS, GRID_DEPTH_BINS), MAX_DETECTION_RANGE)
    
    # 在前方中央放置一个障碍物
    center_h = GRID_HORIZONTAL_BINS // 2
    center_v = GRID_VERTICAL_BINS // 2
    
    # 在前方几层网格中放置障碍物
    for d in range(3):  # 前3层
        for h in range(center_h - 2, center_h + 3):  # 中央附近
            for v in range(center_v - 1, center_v + 2):  # 中央附近
                if 0 <= h < GRID_HORIZONTAL_BINS and 0 <= v < GRID_VERTICAL_BINS:
                    grid_3d[h, v, d] = 2.0  # 设置为2米距离的障碍物
    
    return grid_3d

def debug_safety_evaluation():
    """
    调试安全性评估
    """
    print("=== 调试安全性评估 ===")
    
    # 创建测试网格
    grid_3d = create_test_grid_with_obstacle()
    
    print(f"网格形状: {grid_3d.shape}")
    print(f"最小距离: {np.min(grid_3d):.2f}m")
    print(f"最大距离: {np.max(grid_3d):.2f}m")
    
    # 评估安全性
    safety_grid = evaluate_grid_safety(grid_3d, MIN_OBSTACLE_DISTANCE)
    
    print(f"安全性网格形状: {safety_grid.shape}")
    print(f"最小安全性: {np.min(safety_grid):.3f}")
    print(f"最大安全性: {np.max(safety_grid):.3f}")
    
    # 打印前几层的安全性
    print("\n前3层网格的安全性分布:")
    for d in range(min(3, GRID_DEPTH_BINS)):
        print(f"\n第{d}层 (深度层):")
        for v in range(GRID_VERTICAL_BINS):
            row = []
            for h in range(GRID_HORIZONTAL_BINS):
                row.append(f"{safety_grid[h, v, d]:.2f}")
            print(f"  垂直{v}: " + " ".join(row))
    
    return grid_3d, safety_grid

def debug_direction_finding():
    """
    调试方向查找
    """
    print("\n=== 调试方向查找 ===")
    
    # 创建测试网格
    grid_3d = create_test_grid_with_obstacle()
    
    # 找到最安全方向
    velocity_vector = find_safest_grid_direction(
        grid_3d,
        base_speed=FORWARD_SPEED,
        min_distance=MIN_OBSTACLE_DISTANCE,
        max_range=MAX_DETECTION_RANGE
    )
    
    print(f"最佳速度向量: Vx={velocity_vector[0]:.3f}, Vy={velocity_vector[1]:.3f}, Vz={velocity_vector[2]:.3f}")
    print(f"速度大小: {np.linalg.norm(velocity_vector):.3f}m/s")
    
    # 计算方向角度
    if abs(velocity_vector[0]) > 0.001:
        angle_deg = np.degrees(np.arctan2(velocity_vector[1], velocity_vector[0]))
        print(f"水平转向角: {angle_deg:.2f}度")
    
    if abs(np.linalg.norm(velocity_vector[:2])) > 0.001:
        elevation_angle = np.degrees(np.arctan2(velocity_vector[2], np.linalg.norm(velocity_vector[:2])))
        print(f"俯仰角: {elevation_angle:.2f}度")
    
    return velocity_vector

def debug_detailed_scoring():
    """
    详细调试评分过程
    """
    print("\n=== 详细调试评分过程 ===")
    
    grid_3d = create_test_grid_with_obstacle()
    safety_grid = evaluate_grid_safety(grid_3d, MIN_OBSTACLE_DISTANCE)
    
    grid_h_bins, grid_v_bins, grid_d_bins = grid_3d.shape
    
    # 网格边界
    max_range = MAX_DETECTION_RANGE
    y_min, y_max = -max_range * 0.7, max_range * 0.7
    z_min, z_max = -max_range * 0.5, max_range * 0.5
    x_min, x_max = 0.5, max_range
    
    # 网格单元大小
    h_step = (y_max - y_min) / grid_h_bins
    v_step = (z_max - z_min) / grid_v_bins
    d_step = (x_max - x_min) / grid_d_bins
    
    print(f"网格边界: y=[{y_min:.1f}, {y_max:.1f}], z=[{z_min:.1f}, {z_max:.1f}], x=[{x_min:.1f}, {x_max:.1f}]")
    print(f"网格步长: h_step={h_step:.2f}, v_step={v_step:.2f}, d_step={d_step:.2f}")
    
    # 找到最安全的网格
    best_safety = -1.0
    best_h, best_v, best_d = 0, 0, 0
    best_scores = []
    
    # 只考虑前方5层网格
    for d in range(min(5, grid_d_bins)):
        for h in range(grid_h_bins):
            for v in range(grid_v_bins):
                safety = safety_grid[h, v, d]
                
                # 添加方向偏好
                direction_bonus = 0.0
                
                # 前方偏好
                direction_bonus += (grid_d_bins - d) / grid_d_bins * 0.2
                
                # 中央偏好
                h_center = grid_h_bins // 2
                v_center = grid_v_bins // 2
                h_penalty = abs(h - h_center) / max(h_center, 1)
                v_penalty = abs(v - v_center) / max(v_center, 1)
                direction_bonus += (1.0 - h_penalty * 0.3)
                direction_bonus += (1.0 - v_penalty * 0.2)
                
                total_score = safety + direction_bonus
                
                # 记录前10个最高分
                best_scores.append((total_score, h, v, d, safety, direction_bonus))
                
                if total_score > best_safety:
                    best_safety = total_score
                    best_h, best_v, best_d = h, v, d
    
    # 排序并显示前10个最高分
    best_scores.sort(reverse=True)
    print(f"\n前10个最高分网格:")
    for i, (total_score, h, v, d, safety, bonus) in enumerate(best_scores[:10]):
        center_y = y_min + (h + 0.5) * h_step
        center_z = z_min + (v + 0.5) * v_step
        center_x = x_min + (d + 0.5) * d_step
        print(f"  {i+1}. 网格[{h},{v},{d}] 总分:{total_score:.3f} (安全:{safety:.3f} + 偏好:{bonus:.3f}) 位置:({center_x:.1f},{center_y:.1f},{center_z:.1f})")
    
    print(f"\n最佳网格: [{best_h}, {best_v}, {best_d}], 总分: {best_safety:.3f}")
    
    # 计算最佳网格的位置
    center_y = y_min + (best_h + 0.5) * h_step
    center_z = z_min + (best_v + 0.5) * v_step
    center_x = x_min + (best_d + 0.5) * d_step
    
    print(f"最佳网格中心位置: ({center_x:.2f}, {center_y:.2f}, {center_z:.2f})")
    
    # 计算方向向量
    direction_vector = np.array([center_x, center_y, center_z])
    direction_norm = np.linalg.norm(direction_vector)
    
    if direction_norm > 0:
        velocity_vector = direction_vector / direction_norm * FORWARD_SPEED
        print(f"归一化前方向向量: ({direction_vector[0]:.2f}, {direction_vector[1]:.2f}, {direction_vector[2]:.2f})")
        print(f"方向向量长度: {direction_norm:.2f}")
        print(f"最终速度向量: ({velocity_vector[0]:.3f}, {velocity_vector[1]:.3f}, {velocity_vector[2]:.3f})")

if __name__ == "__main__":
    print("开始调试避障算法...")
    
    # 调试安全性评估
    grid_3d, safety_grid = debug_safety_evaluation()
    
    # 调试方向查找
    velocity_vector = debug_direction_finding()
    
    # 详细调试评分过程
    debug_detailed_scoring()
    
    print("\n调试完成!")
